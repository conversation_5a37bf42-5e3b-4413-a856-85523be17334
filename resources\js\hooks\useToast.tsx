import { useState, useCallback } from 'react';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
    id: string;
    type: ToastType;
    title?: string;
    message: string;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
}

interface ToastState {
    toasts: Toast[];
}

let toastCounter = 0;

export function useToast() {
    const [state, setState] = useState<ToastState>({ toasts: [] });

    const addToast = useCallback((toast: Omit<Toast, 'id'>) => {
        const id = `toast-${++toastCounter}`;
        const newToast: Toast = {
            id,
            duration: 5000, // Default 5 seconds
            ...toast,
        };

        setState(prev => ({
            toasts: [...prev.toasts, newToast],
        }));

        // Auto remove after duration
        if (newToast.duration && newToast.duration > 0) {
            setTimeout(() => {
                removeToast(id);
            }, newToast.duration);
        }

        return id;
    }, []);

    const removeToast = useCallback((id: string) => {
        setState(prev => ({
            toasts: prev.toasts.filter(toast => toast.id !== id),
        }));
    }, []);

    const clearAllToasts = useCallback(() => {
        setState({ toasts: [] });
    }, []);

    // Convenience methods
    const success = useCallback((message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
        return addToast({ type: 'success', message, ...options });
    }, [addToast]);

    const error = useCallback((message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
        return addToast({ type: 'error', message, duration: 7000, ...options }); // Errors stay longer
    }, [addToast]);

    const warning = useCallback((message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
        return addToast({ type: 'warning', message, ...options });
    }, [addToast]);

    const info = useCallback((message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
        return addToast({ type: 'info', message, ...options });
    }, [addToast]);

    return {
        toasts: state.toasts,
        addToast,
        removeToast,
        clearAllToasts,
        success,
        error,
        warning,
        info,
    };
}

// Global toast instance for use outside of React components
class GlobalToast {
    private listeners: Array<(toasts: Toast[]) => void> = [];
    private toasts: Toast[] = [];

    subscribe(listener: (toasts: Toast[]) => void) {
        this.listeners.push(listener);
        return () => {
            this.listeners = this.listeners.filter(l => l !== listener);
        };
    }

    private notify() {
        this.listeners.forEach(listener => listener(this.toasts));
    }

    addToast(toast: Omit<Toast, 'id'>) {
        const id = `global-toast-${++toastCounter}`;
        const newToast: Toast = {
            id,
            duration: 5000,
            ...toast,
        };

        this.toasts = [...this.toasts, newToast];
        this.notify();

        if (newToast.duration && newToast.duration > 0) {
            setTimeout(() => {
                this.removeToast(id);
            }, newToast.duration);
        }

        return id;
    }

    removeToast(id: string) {
        this.toasts = this.toasts.filter(toast => toast.id !== id);
        this.notify();
    }

    success(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) {
        return this.addToast({ type: 'success', message, ...options });
    }

    error(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) {
        return this.addToast({ type: 'error', message, duration: 7000, ...options });
    }

    warning(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) {
        return this.addToast({ type: 'warning', message, ...options });
    }

    info(message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) {
        return this.addToast({ type: 'info', message, ...options });
    }
}

export const globalToast = new GlobalToast();



