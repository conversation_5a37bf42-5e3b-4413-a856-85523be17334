import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/Sidebar';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Link, usePage } from '@inertiajs/react';
import { useState } from 'react';
import { NavGroup } from '@/types';

/**
 * 主導航組件(側邊導航)
 *
 * 僅提供後台使用
 */
export function NavMain({ groups }: NavGroup) {
    const page = usePage();
    const [openGroups, setOpenGroups] = useState<Record<string, boolean>>({});

    const toggleGroup = (title: string) => {
        setOpenGroups((prev) => ({ ...prev, [title]: !prev[title] }));
    };

    return (
        <>
            {groups.map((group) => {
                const isOpen = openGroups[group.title] ?? true;

                return (
                    <SidebarGroup key={group.title} className="px-2 py-0">
                        <SidebarGroupLabel
                            onClick={() => group.collapsible && toggleGroup(group.title)}
                            className={`flex cursor-pointer items-center justify-between ${group.collapsible ? 'hover:text-primary' : ''}`}
                        >
                            {group.title}
                            {group.collapsible && (isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />)}
                        </SidebarGroupLabel>

                        {(!group.collapsible || isOpen) && (
                            <SidebarMenu>
                                {group.items.map((item) => (
                                    <SidebarMenuItem key={item.title}>
                                        <SidebarMenuButton asChild isActive={page.url.startsWith(item.href)} tooltip={{ children: item.title }}>
                                            <Link href={item.href} prefetch>
                                                {item.icon && <item.icon className="mr-2 h-4 w-4" />}
                                                <span>{item.title}</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                ))}
                            </SidebarMenu>
                        )}
                    </SidebarGroup>
                );
            })}
        </>
    );
}
