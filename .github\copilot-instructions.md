# GitHub Copilot 自訂指引（中文）

## ✅ 專案技術棧

- 後端框架：Laravel 12（PHP 8.2+）
- 前端框架：Inertia.js（React + TypeScript）
- UI 組件庫：Tailwind CSS + Radix UI + shadcn/ui
- 路由方式：Laravel 路由 + Inertia 頁面渲染

---

## ✅ 編碼規範與最佳實踐

### Laravel 控制器與路由

- 每個功能僅綁定一組路由，避免重複建立相似功能的多個路由。
- 控制器方法應遵循 RESTful 命名規則：
    - `index`, `show`, `create`, `store`, `edit`, `update`, `destroy`

---

### Inertia.js 使用方式

- 所有頁面回傳皆使用 `Inertia::render()`。
- 避免使用 Blade 模板與 `redirect()`（除非必要）。
- 前端頁面跳轉與表單提交應使用：
    - `<Link />` 元件
    - `useForm()` 表單 hook

---

## ✅ UI 與樣式規範

### 組件開發

- 優先使用 **shadcn/ui** 提供的 React 組件。
- 若需自訂元件，應基於 **Radix UI** 搭配 **Tailwind CSS** 實作。
- 禁止使用：
    - CSS 模組 (`*.module.css`)
    - styled-components 或其他樣式解決方案
- 建立新元件前，應先檢查專案中是否已有類似元件可重用。
- 元件命名與檔案命名應一致，並遵循一致的格式。

---

## ✅ 頁面與元件結構

- 建立新頁面或元件前，應確認是否已有相同功能存在。
- 所有元件與頁面應依據功能模組劃分資料夾，例如：
    - `Users/`, `Dashboard/`, `Settings/`
- 避免在不同資料夾內建立功能重複的元件。

---

## ✅ 資料傳遞與表單處理

- 後端傳遞資料應透過 Inertia 的 `props`。
- 所有表單操作應使用 `useForm()` 管理狀態與提交流程。
- 前端 **不得直接使用 `fetch()` 或 axios** 呼叫 API，除非：
    - 上傳檔案
    - 輪詢（polling）
    - 第三方整合

---

## ✅ 命名與一致性

- 控制器、路由、前端元件間應保持命名一致性。
- 命名風格統一如下：
    - React 元件檔案與元件名稱：**PascalCase**
    - Laravel 控制器：以 `Controller` 為結尾（如 `UserController`）
    - Blade 或 Vue 檔案（如有）：**kebab-case** 或 **小駝峰命名**
- TypeScript 中應為：
    - props 定義明確型別
    - 表單資料結構進行類型註解

---

## 💡 Copilot 提示

- 建議優先產出符合上述規範的程式碼。
- 表單驗證請使用 Laravel 的 **FormRequest**，避免在控制器中手寫 rules。
- 導頁建議使用：
    - `router.visit(...)`
    - `<Link>` 元件
- 建立新元件前，應先搜尋是否已有可重用的元件。

---

## ✅ 命名一致性與重構指引

- 檔案名稱與檔案內的元件名稱必須一致：
    - 例如：`UserList.tsx` 應包含 `export default function UserList()`
- 若發現命名與引用不一致，應執行以下重構流程：
    - 修改檔名為與元件名稱一致（使用 PascalCase）
    - 修改元件名稱為與檔名一致
    - 搜尋並更新所有引用點（例如 `import`, `route()`, `use()`）

---

## ✅ React 元件命名策略

- **一律使用 PascalCase 命名 React 元件與檔案**
    - ✅ 正確：`ConfirmDialog.tsx`, `UserCard.tsx`
    - ❌ 錯誤：`confirm-dialog.tsx`, `user-card.tsx`
- 對於早期 Inertia scaffold 自動產生的 kebab-case 檔案（如 `app-logo.tsx`）：
    - 可暫時保留
    - 若進行重構時遇到，應同步：
        - 將檔名改為 PascalCase
        - 調整元件名稱一致
        - 更新所有 import 路徑與使用位置

---
