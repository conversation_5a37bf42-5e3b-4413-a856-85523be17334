<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;

use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Auth\RecommenderAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 認證路由
|--------------------------------------------------------------------------
|
| 這裡定義所有與使用者認證相關的路由，針對不同角色的專用登入路由
|
*/

/**
 * 報名系統考生登入
 *
 * 將從報名系統收到的 token 送去 API系統 進行解碼和驗證
 * 取得考生資料後登入後建立或更新考生，並登入系統
 */

// 訪客路由 (未登入使用者可訪問)
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthenticatedSessionController::class, 'index'])->name('login'); // 管理員登入頁面

    Route::post('/login', [AuthenticatedSessionController::class, 'store']);                                                        // 管理員 登入
    Route::get('/auth-from-external', [ApplicantLoginController::class, 'handleExternalAuth'])->name('applicant.externalLogin');    // 考生   登入(從報名系統)
    Route::get('/recommender/auth/{token}', [RecommenderAuthController::class, 'authenticateWithToken'])->name('recommender.auth'); // 推薦人 登入(token)
});

// 已認證使用者路由 (需要登入)
Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout'); // 通一登出邏輯
});
