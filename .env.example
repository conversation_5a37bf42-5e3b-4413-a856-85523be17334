APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=zh-TW
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug # 開發階段使用 debug 等級，正式環境可改為 info 或 warning，避免過多日誌輸出

# DB
DB_CONNECTION=mysql
DB_HOST=
DB_PORT=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database # 使用 database 連接，方便管理和監控(需透過 php artisan queue:work 啟動工作隊列)

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# ==============================================================================
# 推薦函系統設定
# ==============================================================================
RECOMMENDATION_VERSION=1.0.0
RECOMMENDATION_ADMIN_EMAIL=
RECOMMENDATION_SUPPORT_EMAIL=
RECOMMENDATION_SUPPORT_TEL=

# PDF設定(用於問卷轉換成PDF的相關設定)
PDF_ENGINE=dompdf # 使用 dompdf 作為 PDF 引擎
PDF_PAPER_SIZE=A4  # 預設紙張大小為 A4，可選擇 A3, Letter 等
PDF_ORIENTATION=portrait # 預設為直式，橫式可改為 landscape
PDF_DPI=96 # 預設解析度為 96 DPI
PDF_DEFAULT_FONT="DejaVu Sans" # 預設字體為 DejaVu Sans，支援多語系
PDF_CHINESE_FONT="Microsoft JhengHei" # 中文字體設定，使用微軟正黑體
PDF_STORAGE_DISK=local # PDF 檔案儲存磁碟，使用 local 磁碟
PDF_STORAGE_PATH=recommendations # PDF 檔案儲存路徑
PDF_MAX_SIZE=10240 # PDF 檔案最大大小，單位為 KB

# 檔案上傳設定
UPLOAD_ALLOWED_TYPES=pdf
UPLOAD_MAX_SIZE=10240 # 上傳檔案最大大小，單位為 KB

# 郵件 API 設定
MAIL_EXTERNAL_API_ENABLED=true
MAIL_EXTERNAL_API_URL= # http://example.com/sendmail.php
MAIL_EXTERNAL_API_ACCOUNT= # 帳號 
MAIL_EXTERNAL_API_PASSWORD= # 密碼
MAIL_EXTERNAL_API_REPLY_TO="${MAIL_FROM_ADDRESS}"

# eapapi系統
EAPAPI_BASE_URL=http://docker-compose-eapapi-1/index.php/api/v1/recommendation_system
EAPAPI_API_KEY= # 用於 eapapi 系統的 API 金鑰

# exam系統
EXAM_BASE_URL=http://docker-compose-exam-1/index.php
EXAM_API_KEY= # 用於 exam 系統的 API 金鑰

# 允許的 IP 列表(供config/api.php使用)
EAPAPI_API_IP=
EXAM_API_IP=

# API密鑰(呼叫端要求時帶上)
API_SECRET=

# API User-Agent 白名單
API_WHITELIST_USER_AGENTS="允許的User-Agent,curl等" # 使用逗號分隔，不可有空格

# 上線前須知
# 1. 請重新產生APP_KEY: php artisan key:generate
# 2. 請更新密鑰
# 3. 請確認API_BASE_URL指向正確的外部系統API端點
# 4. 請設定正確的API_WHITELIST_IPS，限制只有特定IP可以訪問API