import React, { useEffect, useMemo, useState } from 'react';
import { CheckCircle } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';
import { Textarea } from '@/components/ui/Textarea';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';

interface Question {
    id: string;
    question?: string;
    label?: string;
    label_en?: string; // 英文標籤
    type: 'text' | 'textarea' | 'radio';
    required: boolean;
    options?: string[];
    options_en?: string[]; // 英文選項
    max_length?: number;
    placeholder?: string;
    placeholder_en?: string; // 英文佔位符
}

interface QuestionnaireTemplate {
    id: number;
    department_name: string;
    program_type: string;
    template_name: string;
    questions: Question[];
}

interface ApplicantInfo {
    name: string;
    department_name: string;
    program_type: string;
}

interface DynamicQuestionnaireProps {
    template: QuestionnaireTemplate;
    initialData?: { [key: string]: any };
    onSubmit: (data: { [key: string]: any }) => void;
    onCancel: () => void;
    isSubmitting?: boolean;
    applicantInfo?: ApplicantInfo; // 考生資訊
}

/**
 * 動態問卷組件
 *
 * 根據資料庫的問卷模板動態生成問卷表單。
 * 此組件不使用 i18n，因為問卷模板的中英文資料來源為資料庫。
 */
export default function DynamicQuestionnaire({
    template,
    initialData = {},
    onSubmit,
    onCancel,
    isSubmitting = false,
    applicantInfo,
}: DynamicQuestionnaireProps) {
    const [formData, setFormData] = useState<{ [key: string]: any }>(initialData);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const { language } = useLanguage(); // 使用語言鉤子來獲取當前語言

    // 初始化表單資料
    useEffect(() => {
        setFormData(initialData);
    }, [initialData]);

    // 檢查是否可以提交
    const canSubmit = useMemo(() => {
        return template.questions.every((question) => {
            if (!question.required) return true;
            const value = formData[question.id];
            return value && (typeof value !== 'string' || value.trim() !== '');
        });
    }, [formData, template.questions]);

    // 處理輸入變更
    const handleInputChange = (questionId: string, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [questionId]: value,
        }));

        // 清除錯誤訊息，如果使用者開始輸入
        if (errors[questionId]) {
            setErrors((prev) => {
                const newErrors = { ...prev };
                delete newErrors[questionId];
                return newErrors;
            });
        }
    };

    // 驗證表單
    const validateForm = (): boolean => {
        const newErrors: { [key: string]: string } = {};

        template.questions.forEach((question) => {
            if (question.required) {
                const value = formData[question.id];
                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    newErrors[question.id] = '此欄位為必填';
                }
            }

            if (question.max_length && formData[question.id]) {
                const value = formData[question.id].toString();
                if (value.length > question.max_length) {
                    newErrors[question.id] = `內容不能超過 ${question.max_length} 字`;
                }
            }
        });

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // 提交表單
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (validateForm()) {
            onSubmit(formData);
        }
    };

    // 渲染每個問題
    const renderQuestion = (question: Question, index: number) => {
        const value = formData[question.id] || '';
        const error = errors[question.id];
        const questionNumber = index + 1;

        switch (question.type) {
            // 一般輸入
            case 'text':
                return (
                    <div key={question.id} className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                        <div className="flex items-start gap-3">
                            <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-blue-100 text-sm font-bold text-blue-600">
                                {questionNumber}
                            </div>

                            <div className="flex-1 space-y-3">
                                <div className="space-y-1">
                                    <Label htmlFor={question.id} className="block text-base font-semibold text-gray-900">
                                        {(language === 'en' && question.label_en) || question.label}
                                        {question.required && <span className="ml-1 text-red-500">*</span>}
                                    </Label>
                                </div>

                                <Input
                                    id={question.id}
                                    type="text"
                                    value={value}
                                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                                    placeholder={question.placeholder_en ? question.placeholder_en : question.placeholder}
                                    maxLength={question.max_length}
                                    className={`${error ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'}`}
                                />
                            </div>
                        </div>

                        {(error || question.max_length) && (
                            <div className="mt-1 flex flex-col items-start justify-between gap-1 pl-12 sm:flex-row sm:items-center">
                                {error && <div className="text-sm font-medium text-red-500">{error}</div>}
                                {question.max_length && (
                                    <div className="ml-auto text-sm text-gray-500">
                                        {value.length}/{question.max_length}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                );

            // 長文本輸入
            case 'textarea':
                return (
                    <div key={question.id} className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                        <div className="flex items-start gap-3">
                            <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-blue-100 text-sm font-bold text-blue-600">
                                {questionNumber}
                            </div>

                            <div className="flex-1 space-y-3">
                                <div className="space-y-1">
                                    <Label htmlFor={question.id} className="block text-base font-semibold text-gray-900">
                                        {(language === 'en' && question.label_en) || question.label}
                                        {question.required && <span className="ml-1 text-red-500">*</span>}
                                    </Label>
                                </div>

                                <Textarea
                                    id={question.id}
                                    value={value}
                                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                                    placeholder={question.placeholder_en ? question.placeholder_en : question.placeholder}
                                    maxLength={question.max_length}
                                    className={`min-h-[120px] resize-none ${
                                        error ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'
                                    }`}
                                />

                                {(error || question.max_length) && (
                                    <div className="mt-1 flex flex-col items-start justify-between gap-1 pl-1 sm:flex-row sm:items-center">
                                        {error && <div className="text-sm font-medium text-red-500">{error}</div>}
                                        {question.max_length && (
                                            <div className="ml-auto text-sm text-gray-500">
                                                {value.length}/{question.max_length}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                );

            // 單選
            case 'radio':
                return (
                    <div key={question.id} className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                        <div className="flex items-start gap-3">
                            <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-blue-100 text-sm font-bold text-blue-600">
                                {questionNumber}
                            </div>

                            <div className="flex-1 space-y-4">
                                <div className="space-y-1">
                                    <Label className="block text-base font-semibold text-gray-900">
                                        {(language === 'en' && question.label_en) || question.label}
                                        {question.required && <span className="ml-1 text-red-500">*</span>}
                                    </Label>
                                </div>

                                <div className="grid gap-3 md:grid-cols-5">
                                    {question.options?.map((option, optionIndex) => {
                                        const isSelected = value === option;
                                        const isFirst = optionIndex === 0;
                                        const isLast = optionIndex === (question.options?.length ?? 0) - 1;
                                        const bgClass = isSelected
                                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                                            : `border-gray-200 hover:border-gray-300 ${
                                                  isFirst ? 'bg-red-50' : isLast ? 'bg-green-50' : 'bg-gray-50'
                                              }`;

                                        return (
                                            <label
                                                key={optionIndex}
                                                className={`flex cursor-pointer items-center space-x-2 rounded-lg border px-3 py-2 transition-colors hover:bg-blue-50 ${bgClass}`}
                                            >
                                                <input
                                                    type="radio"
                                                    name={question.id}
                                                    value={option}
                                                    checked={isSelected}
                                                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                                                    className="text-blue-600 focus:ring-blue-500"
                                                />
                                                <span className="text-sm font-medium text-gray-700">
                                                    {language === 'en' ? question.options_en?.[optionIndex] : option}
                                                </span>
                                            </label>
                                        );
                                    })}
                                </div>

                                {error && <div className="text-sm font-medium text-red-500">{error}</div>}
                            </div>
                        </div>
                    </div>
                );

            default:
                return <>資料異常，請聯繫管理員</>;
        }
    };

    return (
        <div className="w-full space-y-6">
            {/* 問卷標題 */}
            <div className="rounded-lg bg-gradient-to-r from-green-50 to-blue-50 p-6">
                <div className="space-y-4">
                    <div>
                        <h2 className="text-xl font-bold text-gray-900">{template.template_name}</h2>
                        <p className="text-base text-gray-600">
                            {template.department_name} – {template.program_type}
                        </p>
                    </div>
                </div>
            </div>

            {/* 問卷內容 */}
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-6">{template.questions.map((question, index) => renderQuestion(question, index))}</div>

                {/* 提交按鈕區域 */}
                {/* todo UI項目優化，當在小裝置時，若還沒有滾動到底部，只顯示applicantInfo，不顯示按鈕，直到滾動到底部才顯示按鈕 */}
                <div className="sticky bottom-0 flex flex-col gap-y-4 rounded-lg border-t bg-white p-6 shadow-lg sm:flex-row sm:items-center sm:justify-between sm:gap-y-0">
                    <div className="w-full text-sm text-gray-600 sm:w-auto">
                        {canSubmit ? (
                            <div className="flex items-center gap-2 text-green-600">
                                <CheckCircle className="h-4 w-4 shrink-0" />
                                <span className="break-words">所有必填題目已完成，可以提交</span>
                            </div>
                        ) : (
                            <div className="text-amber-600">
                                還有{' '}
                                {
                                    template.questions.filter(
                                        (q) =>
                                            q.required && (!formData[q.id] || (typeof formData[q.id] === 'string' && formData[q.id].trim() === '')),
                                    ).length
                                }{' '}
                                個必填題目未完成
                            </div>
                        )}

                        {applicantInfo && (
                            <div className="mt-1 flex items-center gap-2 text-gray-500">
                                <p className="text-sm break-words">
                                    {applicantInfo.name} - {applicantInfo.department_name} ({applicantInfo.program_type})
                                </p>
                            </div>
                        )}
                    </div>

                    <div className="flex w-full flex-col-reverse gap-2 sm:w-auto sm:flex-row sm:justify-end sm:gap-3">
                        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
                            取消
                        </Button>
                        <Button type="submit" disabled={!canSubmit || isSubmitting} className="min-w-[100px]">
                            {isSubmitting ? '提交中...' : '提交問卷'}
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
}
