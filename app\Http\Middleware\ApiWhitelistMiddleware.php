<?php

namespace App\Http\Middleware;

use App\Models\SystemLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * API白名單中間件
 * 
 * 僅允許白名單內的特定系統呼叫API端點，增強系統安全性
 */
class ApiWhitelistMiddleware
{
    /**
     * 獲取允許的IP白名單
     *
     * @return array
     */
    private function getAllowedIps(): array
    {
        return config('api.whitelist.ips', ['127.0.0.1', '::1', 'localhost']);
    }

    /**
     * 獲取允許的API密鑰
     *
     * @return array
     */
    private function getAllowedApiKeys(): array
    {
        $secret = config('api.secret');
        return array_filter([$secret]);
    }

    /**
     * 獲取允許的User-Agent模式
     *
     * @return array
     */
    private function getAllowedUserAgents(): array
    {
        return config('api.whitelist.user_agents', ['Laravel-RecommendationSystem', 'ExternalSystem-API', 'curl']);
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        /**
         * 記錄API訪問嘗試
         * 
         * 此API是由報名系統向推薦函系統發出的請求，並非用戶端的IP或User-Agent
         */
        $clientIp = $this->getClientIp($request); // 獲取客戶端(報名系統)真實IP
        $userAgent = $request->userAgent();
        $apiKey = $request->input('rec_api_key') ?? $request->query('rec_api_key') ?? $request->header('X-API-Key');

        Log::info('API訪問嘗試', [
            'ip' => $clientIp,
            'user_agent' => $userAgent,
            'has_api_key' => !empty($apiKey),
            'api_key' => $apiKey,
            'endpoint' => $request->path(),
            'method' => $request->method()
        ]);

        Log::debug('白名單 IPs', config('api.whitelist.ips'));

        // 檢查IP白名單
        if (!$this->isIpAllowed($clientIp)) {
            $this->logUnauthorizedAccess($request, 'IP不在白名單內', $clientIp);
            return $this->unauthorizedResponse('訪問被拒絕：IP不在允許範圍內');
        }

        Log::debug('IP驗證通過', [
            'ip' => $clientIp,
        ]);

        // 檢查API密鑰
        if (!$this->isApiKeyValid($apiKey)) {
            $this->logUnauthorizedAccess($request, 'API密鑰無效', $clientIp);
            return $this->unauthorizedResponse('訪問被拒絕：API密鑰無效');
        }

        Log::debug('API 密鑰驗證通過', [
            'api_key' => $apiKey
        ]);


        // 檢查User-Agent（可選）
        // if (!$this->isUserAgentAllowed($userAgent)) {
        //     $this->logUnauthorizedAccess($request, 'User-Agent不被允許', $clientIp);
        //     return $this->unauthorizedResponse('訪問被拒絕：請求來源不被允許');
        // }

        // Log::debug('User-Agent驗證通過', [
        //     'user_agent' => $userAgent
        // ]);

        // 記錄成功的API訪問
        SystemLog::logOperation(
            SystemLog::ACTION_VIEW,
            'API訪問成功',
            [
                'ip' => $clientIp,
                'user_agent' => $userAgent,
                'endpoint' => $request->path(),
                'method' => $request->method()
            ]
        );

        Log::debug('API訪問成功', [
            'ip' => $clientIp,
            'user_agent' => $userAgent,
            'endpoint' => $request->path(),
            'method' => $request->method()
        ]);

        return $next($request);
    }

    /**
     * 獲取客戶端真實IP
     * 
     * @param Request $request
     * @return string
     */
    private function getClientIp(Request $request): string
    {
        // 檢查代理伺服器設定的標頭
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // 代理伺服器
            'HTTP_X_FORWARDED_FOR',      // 負載均衡器
            'HTTP_X_FORWARDED',          // 代理伺服器
            'HTTP_X_CLUSTER_CLIENT_IP',  // 叢集
            'HTTP_FORWARDED_FOR',        // 代理伺服器
            'HTTP_FORWARDED',            // 代理伺服器
            'REMOTE_ADDR'                // 標準
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);

                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * 檢查IP是否在白名單內
     *
     * @param string $ip
     * @return bool
     */
    private function isIpAllowed(string $ip): bool
    {
        foreach ($this->getAllowedIps() as $allowedIp) {
            if ($this->ipMatches($ip, $allowedIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 檢查IP是否匹配規則
     * 
     * @param string $ip
     * @param string $rule
     * @return bool
     */
    private function ipMatches(string $ip, string $rule): bool
    {
        // 完全匹配
        if ($ip === $rule) {
            return true;
        }

        // 主機名匹配
        if ($rule === 'localhost' && in_array($ip, ['127.0.0.1', '::1'])) {
            return true;
        }

        // CIDR匹配
        if (strpos($rule, '/') !== false) {
            return $this->ipInCidr($ip, $rule);
        }

        return false;
    }

    /**
     * 檢查IP是否在CIDR範圍內
     * 
     * @param string $ip
     * @param string $cidr
     * @return bool
     */
    private function ipInCidr(string $ip, string $cidr): bool
    {
        list($subnet, $mask) = explode('/', $cidr);

        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
        }

        // IPv6支援可以在這裡添加
        return false;
    }

    /**
     * 檢查API密鑰是否有效
     *
     * @param string|null $apiKey
     * @return bool
     */
    private function isApiKeyValid(?string $apiKey): bool
    {
        if (empty($apiKey)) {
            return false;
        }

        return in_array($apiKey, $this->getAllowedApiKeys());
    }

    /**
     * 檢查User-Agent是否被允許
     *
     * @param string|null $userAgent
     * @return bool
     */
    private function isUserAgentAllowed(?string $userAgent): bool
    {
        if (empty($userAgent)) {
            return false;
        }

        foreach ($this->getAllowedUserAgents() as $allowedAgent) {
            if (strpos($userAgent, $allowedAgent) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 記錄未授權訪問
     * 
     * @param Request $request
     * @param string $reason
     * @param string $ip
     */
    private function logUnauthorizedAccess(Request $request, string $reason, string $ip): void
    {
        Log::warning('API未授權訪問嘗試', [
            'reason' => $reason,
            'ip' => $ip,
            'user_agent' => $request->userAgent(),
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'headers' => $request->headers->all()
        ]);

        SystemLog::logOperation(
            SystemLog::ACTION_VIEW,
            'API未授權訪問嘗試',
            [
                'reason' => $reason,
                'ip' => $ip,
                'endpoint' => $request->path()
            ],
            SystemLog::LEVEL_WARNING
        );
    }

    /**
     * 返回未授權回應
     * 
     * @param string $message
     * @return Response
     */
    private function unauthorizedResponse(string $message): Response
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'error_code' => 'UNAUTHORIZED_ACCESS'
        ], 403);
    }
}
