import AdminLayout from '@/layouts/AdminLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Plus, Edit, Eye, Trash2, Lock, Unlock } from 'lucide-react';
import { useEffect, useState } from 'react';
import { router } from '@inertiajs/react';

interface QuestionnaireTemplate {
    id: number;
    department_name: string;
    program_type: string;
    template_name: string;
    description: string;
    questions: any;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface Question {
    id: string;
    question: string;
    type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox';
    required: boolean;
    options?: string[];
    max_length?: number;
    placeholder?: string;
}

interface QuestionnaireTemplatesProps {
    templates: QuestionnaireTemplate[];
}

export default function QuestionnaireTemplates({ templates }: QuestionnaireTemplatesProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '問卷模板', href: '/admin/questionnaire-templates' },
    ];

    // 狀態徽章
    const getStatusBadge = (isActive: boolean) => {
        return isActive ? (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
                啟用中
            </Badge>
        ) : (
            <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                已停用
            </Badge>
        );
    };

    // 過濾模板
    const filteredTemplates = templates.filter((template) => {
        const matchesSearch =
            template.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.department_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.program_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.description.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus =
            statusFilter === 'all' || (statusFilter === 'active' && template.is_active) || (statusFilter === 'inactive' && !template.is_active);

        const matchesDepartment = departmentFilter === 'all' || template.department_name === departmentFilter;

        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // 獲取所有系所
    const departments = Array.from(new Set(templates.map((template) => template.department_name)));

    // 計算問題數量
    const getQuestionCount = (questions: any) => {
        if (!questions) return 0;
        try {
            const parsed = typeof questions === 'string' ? JSON.parse(questions) : questions;
            return Array.isArray(parsed) ? parsed.length : 0;
        } catch {
            return 0;
        }
    };

    const [isLoading, setIsLoading] = useState(false);
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [editingTemplate, setEditingTemplate] = useState<QuestionnaireTemplate | null>(null);
    const [previewTemplate, setPreviewTemplate] = useState<QuestionnaireTemplate | null>(null);

    // Form state
    const [formData, setFormData] = useState({
        department_name: '',
        program_type: 'master',
        template_name: '',
        questions: [] as Question[],
    });

    // useEffect(() => {
    //     loadTemplates();
    // }, []);

    const loadTemplates = async () => {
        try {
            setIsLoading(true);
            const response = await fetch('admin/questionnaire/templates');
            const data = await response.json();

            // Parse questions JSON if it's a string
            const processedTemplates = (data.templates || []).map((template: any) => ({
                ...template,
                questions: typeof template.questions === 'string' ? JSON.parse(template.questions) : template.questions,
            }));

            // setTemplates(processedTemplates);
        } catch (error) {
            console.error('Error loading templates:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCreateTemplate = () => {
        setFormData({
            department_name: '',
            program_type: 'master',
            template_name: '',
            questions: [],
        });
        setEditingTemplate(null);
        setShowCreateForm(true);
    };

    const handleEditTemplate = (template: QuestionnaireTemplate) => {
        setFormData({
            department_name: template.department_name,
            program_type: template.program_type,
            template_name: template.template_name,
            questions: template.questions,
        });
        setEditingTemplate(template);
        setShowCreateForm(true);
    };

    const handleSaveTemplate = async () => {
        try {
            const response = await fetch('admin/questionnaire/save-template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                await loadTemplates();
                setShowCreateForm(false);
                setEditingTemplate(null);
            } else {
                console.error('Error saving template');
            }
        } catch (error) {
            console.error('Error saving template:', error);
        }
    };

    const handleDeleteTemplate = async (templateId: number) => {
        if (!confirm('確定要刪除此問卷模板嗎？')) return;

        try {
            const response = await fetch(`admin/questionnaire/templates/${templateId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            if (response.ok) {
                await loadTemplates();
            } else {
                console.error('Error deleting template');
            }
        } catch (error) {
            console.error('Error deleting template:', error);
        }
    };

    const addQuestion = () => {
        const newQuestion: Question = {
            id: `q_${Date.now()}`,
            question: '',
            type: 'text',
            required: false,
            placeholder: '',
        };
        setFormData((prev) => ({
            ...prev,
            questions: [...prev.questions, newQuestion],
        }));
    };

    const updateQuestion = (index: number, field: keyof Question, value: any) => {
        setFormData((prev) => ({
            ...prev,
            questions: prev.questions.map((q, i) => (i === index ? { ...q, [field]: value } : q)),
        }));
    };

    const removeQuestion = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            questions: prev.questions.filter((_, i) => i !== index),
        }));
    };

    const getTypeLabel = (type: string) => {
        const labels = {
            text: '單行文字',
            textarea: '多行文字',
            select: '下拉選單',
            radio: '單選',
            checkbox: '多選',
        };
        return labels[type as keyof typeof labels] || type;
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="text-center">
                    <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
                    <p className="text-gray-600">載入問卷模板中...</p>
                </div>
            </div>
        );
    }

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="問卷模板管理(未啟用)" description="管理推薦函問卷模板，設定不同系所和學程的問卷內容">
            <Head title="問卷模板管理" />

            <div className="space-y-6 p-6">
                {/* 操作區域 */}
                <div className="flex items-center justify-between">
                    <Button size="sm" className="flex items-center gap-1" onClick={() => router.get('/admin/questionnaire-templates/create')}>
                        <Plus className="h-3 w-3" />
                        <span className="text-xs">新增模板</span>
                    </Button>
                </div>

                {/* todo 篩選和搜尋區域(元件化) */}

                {/* todo 統計資訊(元件化) */}
                {/* 模板列表 */}
                <Card>
                    <CardHeader>
                        <CardTitle>問卷模板列表</CardTitle>
                        <CardDescription>
                            顯示 {filteredTemplates.length} / {templates.length} 個模板
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredTemplates.map((template) => (
                                <div key={template.id} className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50">
                                    <div className="grid flex-1 grid-cols-1 gap-4 md:grid-cols-4">
                                        <div>
                                            <p className="font-medium">{template.template_name}</p>
                                            <p className="text-sm text-gray-500">{template.description}</p>
                                        </div>
                                        <div>
                                            <p className="font-medium">{template.department_name}</p>
                                            <p className="text-sm text-gray-500">{template.program_type}</p>
                                        </div>
                                        <div>
                                            {getStatusBadge(template.is_active)}
                                            <p className="mt-1 text-sm text-gray-500">{getQuestionCount(template.questions)} 個問題</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                建立於 {new Date(template.created_at).toLocaleDateString('zh-TW')}
                                            </p>
                                            <p className="text-sm text-gray-500">排序：{template.sort_order}</p>
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        <Button variant="outline" size="sm">
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button variant="outline" size="sm">
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ))}

                            {filteredTemplates.length === 0 && <div className="py-8 text-center text-gray-500">沒有找到符合條件的問卷模板</div>}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}



