import { SharedData } from '@/types';
import { usePage } from '@inertiajs/react';

export function AppFooter() {
    const { system_info } = usePage<SharedData>().props;

    const year = new Date().getFullYear();

    return (
        <footer className="border-t border-sidebar-border/80 bg-background text-sm text-muted-foreground">
            <div className="mx-auto flex flex-col items-center justify-between gap-2 px-4 py-6 md:max-w-7xl md:flex-row">
                {/* 左側 - 版權 */}
                <p className="text-center md:text-left">&copy; {year} National United University. All rights reserved.</p>

                {/* 右側 - 聯絡資訊上下排列 */}
                <div className="flex flex-col items-center space-y-1 md:items-end">
                    <a href={`tel:${system_info.support_tel}`} className="hover:underline">
                        Tel: {system_info.support_tel}
                    </a>
                    <a href={`mailto:${system_info.support_email}`} className="hover:underline">
                        Email: {system_info.support_email}
                    </a>
                </div>
            </div>
        </footer>
    );
}
