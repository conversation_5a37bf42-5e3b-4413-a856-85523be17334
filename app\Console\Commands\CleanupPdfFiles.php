<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Models\PdfMergeTask;
use Carbon\Carbon;

class CleanupPdfFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pdf:cleanup {--days=7 : 保留天數} {--dry-run : 僅顯示將要刪除的檔案，不實際刪除}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理過期的PDF壓縮檔案和測試檔案';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $dryRun = $this->option('dry-run');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("開始清理 {$days} 天前的PDF檔案...");
        if ($dryRun) {
            $this->warn('這是預覽模式，不會實際刪除檔案');
        }

        $deletedFiles = 0;
        $deletedSize = 0;

        // 1. 清理過期的任務相關檔案
        $expiredTasks = PdfMergeTask::where('created_at', '<', $cutoffDate)
            ->whereNotNull('zip_file_path')
            ->get();

        $this->info("找到 {$expiredTasks->count()} 個過期任務");

        foreach ($expiredTasks as $task) {
            $zipPath = $task->zip_file_path;
            
            if ($zipPath && Storage::disk('public')->exists($zipPath)) {
                $fileSize = Storage::disk('public')->size($zipPath);
                $filePath = Storage::disk('public')->path($zipPath);
                
                $this->line("任務 {$task->task_id}: {$zipPath} (" . $this->formatBytes($fileSize) . ")");
                
                if (!$dryRun) {
                    Storage::disk('public')->delete($zipPath);
                    $deletedFiles++;
                    $deletedSize += $fileSize;
                    
                    // 清除任務中的檔案路徑
                    $task->update(['zip_file_path' => null]);
                }
            }
        }

        // 2. 清理測試PDF檔案
        $testPdfDir = 'test_pdfs';
        if (Storage::disk('local')->exists($testPdfDir)) {
            $testFiles = Storage::disk('local')->files($testPdfDir);
            
            $this->info("檢查測試PDF檔案目錄...");
            
            foreach ($testFiles as $file) {
                $lastModified = Carbon::createFromTimestamp(Storage::disk('local')->lastModified($file));
                
                if ($lastModified->lt($cutoffDate)) {
                    $fileSize = Storage::disk('local')->size($file);
                    $this->line("測試檔案: {$file} (" . $this->formatBytes($fileSize) . ")");
                    
                    if (!$dryRun) {
                        Storage::disk('local')->delete($file);
                        $deletedFiles++;
                        $deletedSize += $fileSize;
                    }
                }
            }
        }

        // 3. 清理PDF packages目錄中的孤立檔案
        $packagesDir = 'pdf_packages';
        if (Storage::disk('public')->exists($packagesDir)) {
            $packageFiles = Storage::disk('public')->files($packagesDir);
            
            $this->info("檢查PDF packages目錄...");
            
            foreach ($packageFiles as $file) {
                $lastModified = Carbon::createFromTimestamp(Storage::disk('public')->lastModified($file));
                
                if ($lastModified->lt($cutoffDate)) {
                    // 檢查是否有對應的任務記錄
                    $fileName = basename($file);
                    $hasTask = PdfMergeTask::where('zip_file_path', 'like', "%{$fileName}%")->exists();
                    
                    if (!$hasTask) {
                        $fileSize = Storage::disk('public')->size($file);
                        $this->line("孤立檔案: {$file} (" . $this->formatBytes($fileSize) . ")");
                        
                        if (!$dryRun) {
                            Storage::disk('public')->delete($file);
                            $deletedFiles++;
                            $deletedSize += $fileSize;
                        }
                    }
                }
            }
        }

        // 4. 清理過期的任務記錄
        if (!$dryRun) {
            $deletedTasks = PdfMergeTask::where('created_at', '<', $cutoffDate)
                ->where('status', '!=', PdfMergeTask::STATUS_PROCESSING)
                ->delete();
            
            if ($deletedTasks > 0) {
                $this->info("清理了 {$deletedTasks} 個過期任務記錄");
            }
        }

        // 記錄清理結果
        if ($dryRun) {
            $this->info("預覽完成：將刪除 {$deletedFiles} 個檔案，總大小 " . $this->formatBytes($deletedSize));
        } else {
            $this->info("清理完成：刪除了 {$deletedFiles} 個檔案，釋放空間 " . $this->formatBytes($deletedSize));
            
            Log::info('[CLEANUP_COMPLETE]', [
                'deleted_files' => $deletedFiles,
                'deleted_size_bytes' => $deletedSize,
                'deleted_size_formatted' => $this->formatBytes($deletedSize),
                'cutoff_date' => $cutoffDate->toDateTimeString(),
                'retention_days' => $days
            ]);
        }

        return 0;
    }

    /**
     * 格式化檔案大小
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
