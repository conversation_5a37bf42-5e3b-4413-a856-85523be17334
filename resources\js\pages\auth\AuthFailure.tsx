import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AlertCircle, Home, RefreshCw } from 'lucide-react';
import { router } from '@inertiajs/react';
import { useLanguage } from '@/hooks/useLanguage';

interface Props {
    message: string;
    type: 'applicant' | 'recommender';
}

export default function AuthFailure({ message, type }: Props) {
    const { t } = useLanguage();

    const handleRetry = () => {
        window.location.reload();
    };

    const handleGoHome = () => {
        router.visit('/');
    };

    const getTitle = () => {
        switch (type) {
            case 'applicant':
                return t('登入頁面.登入失敗.考生標題');
            case 'recommender':
                return t('登入頁面.登入失敗.推薦人標題');
            default:
                return t('登入頁面.登入失敗.預設標題');
        }
    };

    const getDescription = () => {
        switch (type) {
            case 'applicant':
                return t('登入頁面.登入失敗.考生說明');
            case 'recommender':
                return t('登入頁面.登入失敗.推薦人說明');
            default:
                return t('登入頁面.登入失敗.預設說明');
        }
    };

    return (
        <>
            <Head title={getTitle()} />

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
                <div className="w-full max-w-md space-y-8">
                    <Card className="border-red-200 bg-red-50">
                        <CardHeader className="text-center">
                            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                                <AlertCircle className="h-8 w-8 text-red-600" />
                            </div>
                            <CardTitle className="mt-4 text-xl font-semibold text-red-900">{getTitle()}</CardTitle>
                            <p className="text-sm text-red-700">{getDescription()}</p>
                        </CardHeader>

                        <CardContent className="space-y-6">
                            <div className="rounded-lg border border-red-200 bg-white p-4">
                                <h4 className="mb-2 font-medium text-red-900">{t('登入頁面.登入失敗.錯誤詳情')}</h4>
                                <p className="text-sm text-red-700">{message}</p>
                            </div>

                            {type === 'applicant' && (
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <h4 className="mb-2 font-medium text-blue-900">{t('登入頁面.登入失敗.考生指示')}</h4>
                                    <ul className="space-y-1 text-sm text-blue-700">
                                        <li>• {t('登入頁面.登入失敗.考生指示說明')}</li>
                                        <li>• {t('登入頁面.登入失敗.考生指示說明2')}</li>
                                        <li>• {t('登入頁面.登入失敗.考生指示說明3')}</li>
                                    </ul>
                                </div>
                            )}

                            {type === 'recommender' && (
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <h4 className="mb-2 font-medium text-blue-900">{t('登入頁面.登入失敗.推薦人指示')}</h4>
                                    <ul className="space-y-1 text-sm text-blue-700">
                                        <li>• {t('登入頁面.登入失敗.推薦人指示說明')}</li>
                                        <li>• {t('登入頁面.登入失敗.推薦人指示說明2')}</li>
                                        <li>• {t('登入頁面.登入失敗.推薦人指示說明3')}</li>
                                    </ul>
                                </div>
                            )}

                            <div className="flex flex-col gap-3 sm:flex-row">
                                <Button onClick={handleRetry} variant="outline" className="flex-1">
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    {t('登入頁面.登入失敗.重試')}
                                </Button>
                                <Button onClick={handleGoHome} className="flex-1">
                                    <Home className="mr-2 h-4 w-4" />
                                    {t('登入頁面.登入失敗.回到首頁')}
                                </Button>
                            </div>

                            <div className="text-center">
                                <p className="text-xs text-gray-500">{t('登入頁面.登入失敗.聯繫管理員')}</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
