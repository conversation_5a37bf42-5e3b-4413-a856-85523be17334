## ✅ Laravel 12.x 排程任務的標準設定方式

---

### ✅ 1. 建立 Artisan 命令

在 `app/Console/Commands` 中建立或透過 artisan 命令建立一個php檔案，例如：

```bash
php artisan make:command MyCommand
```

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MyCommand extends Command
{
  protected $signature = 'my:command';
  protected $description = '我的第一個排程任務';

  public function handle()
  {
    try {
      Log::info('我的第一個排程任務執行了');
      // TODO: 加入你的任務邏輯
      return Command::SUCCESS;
    } catch (\Throwable $e) {
      Log::error('我的第一個排程任務失敗了', ['error' => $e->getMessage()]);
      return Command::FAILURE;
    }
  }
}
```

---

### ✅ 2. 註冊路由

在 routes/console.php 中加入：

```php
use Illuminate\Support\Facades\Schedule;

// 排程我的第一個任務
Schedule::command('my:command')
    ->everyFiveMinutes() // 每五分鐘執行一次
    ->withoutOverlapping() // 防止重複執行
    ->runInBackground(); // 在背景執行，避免阻塞其他任務
```

有多種配置方式，這邊只簡單示範每五分鐘執行一次的設定

---

### ✅ 3. 檢查排程是否註冊成功

```bash
php artisan schedule:list
```

你應該會看到：

```
*/5 * * * *  php artisan my:command ← Next Due: 1分鐘後
```

---

### ✅ 4. 確保系統每分鐘觸發 `schedule:run`

這一點與 Laravel 9/10 相同：需要透過 crontab 或其他排程工具，每分鐘執行一次 `php artisan schedule:run`

#### ▶ Linux/macOS：

```bash
crontab -e

* * * * * php artisan schedule:run >> /storage/logs/laravel-schedule.log 2>&1
```

---

### ✅ 5. 日誌驗證執行結果

建議在每個排程中加入 log 或通知，例如：

```php
Log::info('我的第一個排程任務執行了');
```

方便確認是否觸發成功。查看日誌：

```bash
tail -f storage/logs/laravel.log
```

---

## ✅ 總結（Laravel 12.x 任務排程設定流程）

| 步驟                                 | 說明                                 |
| ------------------------------------ | ------------------------------------ |
| 1️⃣ 建立 Artisan 命令                 | `php artisan make:command MyCommand` |
| 2️⃣ 編輯 Artisan 命令                 | 加入你的任務邏輯                     |
| 3️⃣ 註冊路由                          | 在 routes/console.php 中加入排程設定 |
| 4️⃣ 檢查排程是否註冊成功              | `php artisan schedule:list`          |
| 5️⃣ 確保系統每分鐘觸發 `schedule:run` | 使用 crontab 或其他排程工具          |
| 6️⃣ 加入 log 或通知                   | 確認實際執行狀況                     |

---

## 常見問題

- Log紀錄中沒有顯示排程任務的執行狀況
    - 請確認是否已將Log加入到任務中，例如：`Log::info('我的第一個排程任務執行了');`
    - 請確認是否已將排程任務加入到 routes/console.php 中
    - 請確認是否已將排程任務加入到 crontab 中 << 重要
