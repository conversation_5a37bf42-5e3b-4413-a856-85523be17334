<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\RecruitmentPeriodService;
use App\Services\ExternalApiSyncService;
use App\Models\SystemLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

/**
 * 招生期間管理控制器
 */
class RecruitmentPeriodController extends Controller
{
    protected RecruitmentPeriodService $recruitmentService;
    protected ExternalApiSyncService $syncService;

    public function __construct(
        RecruitmentPeriodService $recruitmentService,
        ExternalApiSyncService $syncService
    ) {
        $this->recruitmentService = $recruitmentService;
        $this->syncService = $syncService;
    }

    /**
     * 顯示招生期間管理頁面
     */
    public function index()
    {
        $periods = $this->recruitmentService->getAllPeriods();
        $lastSyncTime = $this->recruitmentService->getLastSyncTime();
        $needsResync = $this->recruitmentService->needsResync();

        // 處理期間資料，添加狀態資訊
        $processedPeriods = array_map(function ($period) {
            return $this->recruitmentService->getPeriodInfo($period['exam_id']);
        }, $periods);

        $breadcrumbs = [
            ['title' => '儀表板', 'href' => '/dashboard'],
            ['title' => '招生期間管理', 'href' => '/admin/recruitment-periods'],
        ];

        return Inertia::render('admin/RecruitmentPeriods', [
            'periods' => $processedPeriods,
            'last_sync_time' => $lastSyncTime?->toISOString(),
            'needs_resync' => $needsResync,
            'active_periods_count' => count($this->recruitmentService->getActiveRecruitmentPeriods()),
            'breadcrumbs' => $breadcrumbs,
        ]);
    }

    /**
     * 手動同步招生期間資料
     */
    public function sync()
    {
        try {
            $result = $this->syncService->syncExamPeriods();

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                "管理員 {$user->name} 手動同步招生期間資料",
                [
                    'sync_result' => $result,
                    'user_id' => $user->id
                ]
            );

            // 清除快取
            $this->recruitmentService->clearCache();

            return back()->with('success', '招生期間資料同步成功');
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                '手動同步招生期間資料失敗',
                $e
            );

            return back()->withErrors([
                'sync' => '同步失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取特定招生期間的詳細資訊
     */
    public function show($examId)
    {
        try {
            $periodInfo = $this->recruitmentService->getPeriodInfo($examId);

            return response()->json([
                'success' => true,
                'data' => $periodInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '獲取招生期間資訊失敗：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 檢查招生期間狀態
     */
    public function checkStatus()
    {
        try {
            $periods = $this->recruitmentService->getAllPeriods();
            $activePeriods = $this->recruitmentService->getActiveRecruitmentPeriods();
            $lastSyncTime = $this->recruitmentService->getLastSyncTime();

            $status = [
                'total_periods' => count($periods),
                'active_periods' => count($activePeriods),
                'last_sync_time' => $lastSyncTime?->toISOString(),
                'needs_resync' => $this->recruitmentService->needsResync(),
                'active_period_details' => $activePeriods,
                'current_time' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '檢查狀態失敗：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清除招生期間快取
     */
    public function clearCache()
    {
        try {
            $this->recruitmentService->clearCache();

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                "管理員 {$user->name} 清除招生期間快取",
                ['user_id' => $user->id]
            );

            return back()->with('success', '快取已清除');
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                '清除招生期間快取失敗',
                $e
            );

            return back()->withErrors([
                'cache' => '清除快取失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 匯出招生期間資料
     */
    public function export()
    {
        try {
            $periods = $this->recruitmentService->getAllPeriods();
            $lastSyncTime = $this->recruitmentService->getLastSyncTime();

            $exportData = [
                'export_time' => now()->toISOString(),
                'last_sync_time' => $lastSyncTime?->toISOString(),
                'periods_count' => count($periods),
                'periods' => $periods
            ];

            $fileName = 'recruitment_periods_' . now()->format('Y-m-d_H-i-s') . '.json';

            return response()->json($exportData)
                ->header('Content-Type', 'application/json')
                ->header('Content-Disposition', "attachment; filename=\"{$fileName}\"");
        } catch (\Exception $e) {
            return back()->withErrors([
                'export' => '匯出失敗：' . $e->getMessage()
            ]);
        }
    }
}
