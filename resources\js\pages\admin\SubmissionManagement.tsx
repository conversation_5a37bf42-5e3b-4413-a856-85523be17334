import React, { useState, useRef } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { Switch } from '@/components/ui/Switch';
import { Badge } from '@/components/ui/Badge';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { Upload, Settings, FileText, Trash2 } from 'lucide-react';

interface QuestionnaireTemplate {
    id: number;
    template_name: string;
    department_name: string;
    program_type: string;
    is_active: boolean;
    created_at: string;
    creator_name: string;
    questions_count: number;
}

interface SubmissionSettings {
    allow_pdf_upload: boolean;
    allow_questionnaire_submission: boolean;
    max_upload_size: string;
    allowed_types: string[];
    storage_path: string;
}

interface SubmissionManagementProps {
    templates: QuestionnaireTemplate[];
    breadcrumbs: Array<{ title: string; href: string }>;
    submission_settings: SubmissionSettings;
}

export default function SubmissionManagement({ templates, breadcrumbs, submission_settings }: SubmissionManagementProps) {
    const [allowPdfUpload, setAllowPdfUpload] = useState(submission_settings.allow_pdf_upload);
    const [allowQuestionnaireSubmission, setAllowQuestionnaireSubmission] = useState(submission_settings.allow_questionnaire_submission);
    const [selectedDepartment, setSelectedDepartment] = useState<string>('');
    const [mergeCondition, setMergeCondition] = useState<string>('');
    const [externalApi, setExternalApi] = useState<string>('http://localhost:18002');
    const fileInputRef = useRef<HTMLInputElement>(null);

    const { data, setData, post, processing, errors, reset } = useForm({
        csv_file: null as File | null,
        department_name: '',
        program_type: '',
        template_name: '',
    });

    // 獲取所有系所
    const departments = Array.from(new Set(templates.map((template) => template.department_name)));

    // 處理檔案選擇
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setData('csv_file', file);

            // 如果未填寫模板名稱，使用檔案名稱（不含副檔名）
            if (!data.template_name) {
                const fileName = file.name.replace(/\.[^/.]+$/, '');
                setData('template_name', fileName);
            }
        }
    };

    // 處理CSV上傳表單提交
    const handleCsvSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/admin/submission/upload-csv', {
            onSuccess: () => {
                reset();
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            },
        });
    };

    // 切換模板狀態
    const toggleTemplateStatus = (id: number) => {
        router.post(`/admin/submission/toggle/${id}`);
    };

    // 刪除模板
    const deleteTemplate = (id: number) => {
        if (confirm('確定要刪除此模板嗎？此操作無法復原。')) {
            router.delete(`/admin/submission/templates/${id}`);
        }
    };

    // 更新提交方式設定
    const updateSubmissionSettings = (
        payload = {
            allow_pdf_upload: allowPdfUpload,
            allow_questionnaire_submission: allowQuestionnaireSubmission,
        },
    ) => {
        if (!payload.allow_pdf_upload && !payload.allow_questionnaire_submission) {
            alert('至少要允許一種提交方式');
            // 重新整理
            window.location.reload();
            return;
        }

        router.post('/admin/submission/submission-settings', payload);
    };

    // 開始資料合併作業
    const startDataMerge = () => {
        if (!mergeCondition.trim()) {
            alert('請輸入合併條件');
            return;
        }

        if (confirm('確定要開始資料合併作業嗎？此操作可能需要較長時間。')) {
            router.post('/admin/submission/start-merge', {
                merge_conditions: { condition: mergeCondition },
                target_exam_id: 'current_exam',
            });
        }
    };

    // 篩選模板
    const filteredTemplates = selectedDepartment ? templates.filter((template) => template.department_name === selectedDepartment) : templates;

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="上傳方式管理" description="管理推薦函提交方式，包括問卷模板和提交設定">
            <Head title="上傳方式管理" />

            <div className="space-y-6 p-6">
                <Tabs defaultValue="templates" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="templates">問卷模板</TabsTrigger>
                        <TabsTrigger value="settings">提交設定</TabsTrigger>
                        <TabsTrigger value="merge">資料合併</TabsTrigger>
                    </TabsList>

                    {/* 問卷模板管理 */}
                    <TabsContent value="templates" className="space-y-6">
                        {/* CSV上傳區域 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Upload className="h-5 w-5" />
                                    上傳問卷模板
                                </CardTitle>
                                <CardDescription>上傳CSV檔案來批量創建問卷模板</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleCsvSubmit} className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                        <div>
                                            <Label htmlFor="department_name">系所名稱</Label>
                                            <Input
                                                id="department_name"
                                                value={data.department_name}
                                                onChange={(e) => setData('department_name', e.target.value)}
                                                placeholder="請輸入系所名稱"
                                                required
                                            />
                                            {errors.department_name && <p className="mt-1 text-sm text-red-600">{errors.department_name}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="program_type">學程類型</Label>
                                            <Input
                                                id="program_type"
                                                value={data.program_type}
                                                onChange={(e) => setData('program_type', e.target.value)}
                                                placeholder="請輸入學程類型"
                                                required
                                            />
                                            {errors.program_type && <p className="mt-1 text-sm text-red-600">{errors.program_type}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="csv_file">CSV檔案</Label>
                                            <Input
                                                id="csv_file"
                                                type="file"
                                                accept=".csv,.txt"
                                                onChange={handleFileChange}
                                                ref={fileInputRef}
                                                required
                                            />
                                            {errors.csv_file && <p className="mt-1 text-sm text-red-600">{errors.csv_file}</p>}
                                        </div>
                                    </div>

                                    <Button type="submit" disabled={processing} className="w-full">
                                        {processing ? '上傳中...' : '上傳模板'}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>

                        {/* 模板列表 */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="flex items-center gap-2">
                                            <FileText className="h-5 w-5" />
                                            問卷模板列表
                                        </CardTitle>
                                        <CardDescription>管理現有的問卷模板</CardDescription>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                                            <SelectTrigger className="w-48">
                                                <SelectValue placeholder="篩選系所" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {/* todo 過濾功能異常(預設值無法顯示全部) */}
                                                <SelectItem value=" ">全部系所</SelectItem>
                                                {departments.map((dept) => (
                                                    <SelectItem key={dept} value={dept}>
                                                        {dept}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {filteredTemplates.length === 0 ? (
                                        <div className="py-8 text-center text-gray-500">
                                            {selectedDepartment ? '該系所暫無問卷模板' : '暫無問卷模板'}
                                        </div>
                                    ) : (
                                        filteredTemplates.map((template) => (
                                            <div key={template.id} className="flex items-center justify-between rounded-lg border p-4">
                                                <div className="flex-1">
                                                    <div className="mb-1 flex items-center gap-2">
                                                        <h3 className="font-medium">{template.template_name}</h3>
                                                        <Badge variant={template.is_active ? 'default' : 'secondary'}>
                                                            {template.is_active ? '啟用' : '停用'}
                                                        </Badge>
                                                    </div>
                                                    <p className="text-sm text-gray-600">
                                                        {template.department_name} - {template.program_type}
                                                    </p>
                                                    <p className="text-xs text-gray-500">
                                                        {template.questions_count} 個問題 • 建立於 {template.created_at}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Button variant="outline" size="sm" onClick={() => toggleTemplateStatus(template.id)}>
                                                        {template.is_active ? '停用' : '啟用'}
                                                    </Button>
                                                    <Button variant="outline" size="sm" onClick={() => deleteTemplate(template.id)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* 提交設定 */}
                    <TabsContent value="settings" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Settings className="h-5 w-5" />
                                    提交方式設定
                                </CardTitle>
                                <CardDescription>設定推薦人可使用的提交方式</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label className="text-base">允許PDF上傳</Label>
                                        <p className="text-sm text-gray-600">推薦人可以直接上傳PDF檔案</p>
                                    </div>
                                    <Switch
                                        checked={allowPdfUpload}
                                        onCheckedChange={(checked) => {
                                            setAllowPdfUpload(checked);
                                            updateSubmissionSettings({
                                                allow_pdf_upload: checked,
                                                allow_questionnaire_submission: allowQuestionnaireSubmission,
                                            });
                                        }}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label className="text-base">允許問卷填寫</Label>
                                        <p className="text-sm text-gray-600">推薦人可以填寫線上問卷</p>
                                    </div>
                                    <Switch
                                        checked={allowQuestionnaireSubmission}
                                        onCheckedChange={(checked) => {
                                            setAllowQuestionnaireSubmission(checked);
                                            updateSubmissionSettings({
                                                allow_pdf_upload: allowPdfUpload,
                                                allow_questionnaire_submission: checked,
                                            });
                                        }}
                                    />
                                </div>

                                <div className="border-t pt-4">
                                    <h4 className="mb-2 font-medium">系統資訊</h4>
                                    <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                                        <div>
                                            <span className="text-gray-600">最大上傳大小：</span>
                                            <span className="font-medium">{submission_settings.max_upload_size}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-600">允許檔案類型：</span>
                                            <span className="font-medium">{submission_settings.allowed_types.join(', ')}</span>
                                        </div>
                                        <div className="md:col-span-2">
                                            <span className="text-gray-600">存儲路徑：</span>
                                            <span className="font-medium">{submission_settings.storage_path}</span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* 資料合併作業 */}
                    <TabsContent value="merge" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    資料合併作業
                                </CardTitle>
                                <CardDescription>向外部系統確認條件並進行推薦函合併作業</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                                    <div className="mb-2 flex items-center gap-2">
                                        <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                                        <span className="font-medium text-yellow-800">功能開發中</span>
                                    </div>
                                    <p className="text-sm text-yellow-700">此功能將包含以下步驟：</p>
                                    <ul className="mt-2 ml-4 space-y-1 text-sm text-yellow-700">
                                        <li>• 向外部系統確認特定條件是否滿足</li>
                                        <li>• 索取相關文件並回傳至系統</li>
                                        <li>• 進行兩個系統的考生推薦函合併</li>
                                        <li>• 產生合併後的PDF壓縮檔</li>
                                    </ul>
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="merge-condition">合併條件設定</Label>
                                        <Input
                                            id="merge-condition"
                                            value={mergeCondition}
                                            onChange={(e) => setMergeCondition(e.target.value)}
                                            placeholder="請輸入合併條件，例如：exam_id=2024"
                                        />
                                        <p className="mt-1 text-xs text-gray-500">設定向外部系統查詢的條件</p>
                                    </div>

                                    <div>
                                        <Label htmlFor="external-api">外部系統API端點</Label>
                                        <Input
                                            id="external-api"
                                            value={externalApi}
                                            onChange={(e) => setExternalApi(e.target.value)}
                                            placeholder="外部系統API地址"
                                        />
                                        <p className="mt-1 text-xs text-gray-500">外部系統的API基礎地址</p>
                                    </div>

                                    <Button onClick={startDataMerge} className="w-full">
                                        開始資料合併作業
                                    </Button>
                                </div>

                                <div className="border-t pt-4">
                                    <h4 className="mb-2 font-medium">合併作業歷史</h4>
                                    <div className="py-8 text-center text-gray-500">暫無合併作業記錄</div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
