import AppHeaderFooterLayout from '@/layouts/app/AppHeaderFooterLayout';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import { type ReactNode } from 'react';

interface AppLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

export default ({ children, breadcrumbs, ...props }: AppLayoutProps) => {
    const { auth } = usePage<SharedData>().props;
    const userRole = auth?.user?.role;

    // 只有管理員使用側邊欄 Layout
    if (userRole === 'admin') {
        return (
            <AppSidebarLayout breadcrumbs={breadcrumbs} {...props}>
                {children}
            </AppSidebarLayout>
        );
    }

    // 其餘使用者皆使用 Header 與 Footer 樣式的 Layout
    return (
        <AppHeaderFooterLayout breadcrumbs={breadcrumbs} {...props}>
            {children}
        </AppHeaderFooterLayout>
    );
};
