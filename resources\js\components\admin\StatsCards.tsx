import { Card, CardContent } from '@/components/ui/Card';
import { LucideIcon } from 'lucide-react';

interface StatCard {
    title: string;
    value: string | number;
    description?: string;
    icon?: LucideIcon;
    color?: 'default' | 'green' | 'red' | 'yellow' | 'blue' | 'purple';
    trend?: {
        value: number;
        isPositive: boolean;
        label: string;
    };
}

interface StatsCardsProps {
    stats: StatCard[];
    className?: string;
    columns?: 1 | 2 | 3 | 4 | 5 | 6;
}

/**
 * 統計卡片元件
 *
 * 顯示一組統計資料卡片
 *
 * @param {StatCard[]} stats - 統計資料陣列
 * @param {string} [className=''] - 附加的 CSS 類名
 * @param {number} [columns=4] - 顯示的列數
 * @returns {JSX.Element} 返回一個統計卡片元件
 */
export default function StatsCards({ stats, className = '', columns = 4 }: StatsCardsProps) {
    const getColorClasses = (color: string) => {
        switch (color) {
            case 'green':
                return {
                    text: 'text-green-600',
                    bg: 'bg-green-50',
                    icon: 'text-green-500',
                };
            case 'red':
                return {
                    text: 'text-red-600',
                    bg: 'bg-red-50',
                    icon: 'text-red-500',
                };
            case 'yellow':
                return {
                    text: 'text-yellow-600',
                    bg: 'bg-yellow-50',
                    icon: 'text-yellow-500',
                };
            case 'blue':
                return {
                    text: 'text-blue-600',
                    bg: 'bg-blue-50',
                    icon: 'text-blue-500',
                };
            case 'purple':
                return {
                    text: 'text-purple-600',
                    bg: 'bg-purple-50',
                    icon: 'text-purple-500',
                };
            default:
                return {
                    text: 'text-gray-900',
                    bg: 'bg-gray-50',
                    icon: 'text-gray-500',
                };
        }
    };

    const getGridCols = (cols: number) => {
        switch (cols) {
            case 1:
                return 'grid-cols-1';
            case 2:
                return 'grid-cols-1 md:grid-cols-2';
            case 3:
                return 'grid-cols-1 md:grid-cols-3';
            case 4:
                return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
            case 5:
                return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-5';
            case 6:
                return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6';
            default:
                return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
        }
    };

    return (
        <div className={`grid gap-4 ${getGridCols(columns)} ${className}`}>
            {stats.map((stat, index) => {
                const colors = getColorClasses(stat.color || 'default');
                const IconComponent = stat.icon;

                return (
                    <Card key={index} className="transition-shadow hover:shadow-md">
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <div className="flex-1">
                                    <div className="mb-2 flex items-center gap-2">
                                        {IconComponent && (
                                            <div className={`rounded-lg p-2 ${colors.bg}`}>
                                                <IconComponent className={`h-4 w-4 ${colors.icon}`} />
                                            </div>
                                        )}
                                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                                    </div>

                                    <div className={`text-2xl font-bold ${colors.text} mb-1`}>{stat.value}</div>

                                    {stat.description && <p className="text-xs text-muted-foreground">{stat.description}</p>}

                                    {stat.trend && (
                                        <div className="mt-2 flex items-center gap-1">
                                            <span className={`text-xs font-medium ${stat.trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                                                {stat.trend.isPositive ? '+' : ''}
                                                {stat.trend.value}%
                                            </span>
                                            <span className="text-xs text-gray-500">{stat.trend.label}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                );
            })}
        </div>
    );
}



