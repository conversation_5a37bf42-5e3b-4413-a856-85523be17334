<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 系統設定模型
 * 
 * 管理系統的各種設定參數，包括時序控制、安全設定、郵件設定等
 */
class SystemSetting extends Model
{
    use HasFactory;

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'description',
        'is_active',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 設定分類常數 -> 這些常數用於標識設定的類別
     */
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_TIMING = 'timing';
    const CATEGORY_SECURITY = 'security';
    const CATEGORY_EMAIL = 'email';

    /**
     * 資料類型常數 -> 這些常數用於標識設定"值"的類型
     */
    const TYPE_STRING = 'string';
    const TYPE_JSON = 'json';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_DATETIME = 'datetime';
    const TYPE_INTEGER = 'integer';

    /**
     * 預設系統設定鍵值 -> 這些常數用於標識系統設定的鍵
     */
    const REMINDER_COOLDOWN_HOURS = 'reminder.cooldown_hours';
    const AUTO_TIMEOUT_DAYS = 'auto.timeout_days';
    const MAINTENANCE_MODE = 'system.maintenance_mode';
    // 推薦人提交方式設定
    const ALLOW_PDF_UPLOAD = 'submission.allow_pdf_upload';
    const ALLOW_QUESTIONNAIRE_SUBMISSION = 'submission.allow_questionnaire_submission';
    // 通用系統資訊
    const SYSTEM_SUPPORT_TEL = 'system.support_tel';
    const SYSTEM_SUPPORT_EMAIL = 'system.support_email';
    const SYSTEM_ADMIN_EMAIL = 'system.admin_email';

    /**
     * 取得設定值
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "system_setting_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)
                ->where('is_active', true)
                ->first();

            if (!$setting) {
                return $default;
            }

            return self::parseValue($setting->value, $setting->type);
        });
    }

    /**
     * 設定值
     */
    public static function set(string $key, $value, string $type = self::TYPE_STRING, string $category = self::CATEGORY_GENERAL, ?string $description = null): bool
    {
        try {
            $formattedValue = self::formatValue($value, $type);

            self::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $formattedValue,
                    'type' => $type,
                    'category' => $category,
                    'description' => $description,
                    'is_active' => true,
                ]
            );

            // 清除快取
            Cache::forget("system_setting_{$key}");

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to set system setting', [
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 解析設定值
     */
    private static function parseValue(string $value, string $type)
    {
        switch ($type) {
            case self::TYPE_JSON:
                return json_decode($value, true);
            case self::TYPE_BOOLEAN:
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case self::TYPE_DATETIME:
                return \Carbon\Carbon::parse($value);
            case self::TYPE_INTEGER:
                return (int) $value;
            default:
                return $value;
        }
    }

    /**
     * 格式化設定值
     */
    private static function formatValue($value, string $type): string
    {
        switch ($type) {
            case self::TYPE_JSON:
                return json_encode($value);
            case self::TYPE_BOOLEAN:
                return $value ? '1' : '0';
            case self::TYPE_DATETIME:
                if ($value instanceof \Carbon\Carbon) {
                    return $value->toDateTimeString();
                }
                return \Carbon\Carbon::parse($value)->toDateTimeString();
            case self::TYPE_INTEGER:
                return (string) (int) $value;
            default:
                return (string) $value;
        }
    }

    /**
     * 檢查使用者是否可以存取系統
     *
     * @param string $userRole 使用者角色
     * @return array 包含是否可存取和原因的陣列
     */
    public static function canUserAccessSystem(string $userRole): array
    {
        // 管理員總是可以存取
        if ($userRole === 'admin') {
            return [
                'can_access' => true,
                'reason' => null
            ];
        }

        return [
            'can_access' => true,
            'reason' => null
        ];
    }

    /**
     * 取得提醒冷卻時間（小時）
     */
    public static function getReminderCooldownHours(): int
    {
        return self::get(self::REMINDER_COOLDOWN_HOURS, 24);
    }

    /**
     * 取得自動超時天數
     */
    public static function getAutoTimeoutDays(): int
    {
        return self::get(self::AUTO_TIMEOUT_DAYS, 7);
    }

    /**
     * 取得支援電話
     */
    public static function getSupportTel(): string
    {
        return self::get(self::SYSTEM_SUPPORT_TEL, '0800-123-456');
    }

    /**
     * 取得支援信箱
     */
    public static function getSupportEmail(): string
    {
        return self::get(self::SYSTEM_SUPPORT_EMAIL, '<EMAIL>');
    }

    /**
     * 取得管理員信箱
     */
    public static function getAdminEmail(): string
    {
        return self::get(self::SYSTEM_ADMIN_EMAIL, '<EMAIL>');
    }

    /**
     * 檢查系統是否處於維護模式
     */
    public static function isMaintenanceMode(): bool
    {
        return self::get(self::MAINTENANCE_MODE, false);
    }

    /**
     * 設定維護模式
     */
    public static function setMaintenanceMode(bool $enabled): bool
    {
        return self::set(
            self::MAINTENANCE_MODE,
            $enabled,
            self::TYPE_BOOLEAN,
            self::CATEGORY_GENERAL,
            '系統維護模式開關'
        );
    }

    /**
     * 檢查是否允許PDF上傳
     */
    public static function isAllowPdfUpload(): bool
    {
        return self::get(self::ALLOW_PDF_UPLOAD, true);
    }

    /**
     * 檢查是否允許問卷提交
     */
    public static function isAllowQuestionnaireSubmission(): bool
    {
        return self::get(self::ALLOW_QUESTIONNAIRE_SUBMISSION, true);
    }

    /**
     * 設定是否允許PDF上傳
     */
    public static function setAllowPdfUpload(bool $enabled): bool
    {
        return self::set(
            self::ALLOW_PDF_UPLOAD,
            $enabled,
            self::TYPE_BOOLEAN,
            self::CATEGORY_GENERAL,
            '允許推薦人上傳PDF檔案'
        );
    }

    /**
     * 設定是否允許問卷提交
     */
    public static function setAllowQuestionnaireSubmission(bool $enabled): bool
    {
        return self::set(
            self::ALLOW_QUESTIONNAIRE_SUBMISSION,
            $enabled,
            self::TYPE_BOOLEAN,
            self::CATEGORY_GENERAL,
            '允許推薦人填寫線上問卷'
        );
    }

    /**
     * 按分類取得設定
     */
    public static function getByCategory(string $category)
    {
        return self::where('category', $category)
            ->where('is_active', true)
            ->orderBy('key')
            ->get();
    }

    /**
     * 檢查特定考試是否在開放期間
     *
     * @param string $examId 考試ID
     * @return bool
     */
    public static function isExamPeriodOpen(string $examId): bool
    {
        // 使用招生期間服務檢查
        $recruitmentService = app(\App\Services\RecruitmentPeriodService::class);
        return $recruitmentService->isInRecruitmentPeriod($examId);
    }

    /**
     * 檢查用戶是否在允許的考試期間內
     *
     * @param string|null $examId 考試ID
     * @param string|null $examYear 考試年度
     * @return array 包含是否允許和原因的陣列
     */
    public static function canUserAccessByExamPeriod(?string $examId, ?string $examYear): array
    {
        if (!$examId || !$examYear) {
            return [
                'can_access' => true,
                'reason' => null
            ];
        }

        // 檢查特定考試期間
        if (!self::isExamPeriodOpen($examId)) {
            return [
                'can_access' => false,
                'reason' => 'exam_period_closed',
                'exam_id' => $examId,
                'exam_year' => $examYear
            ];
        }

        return [
            'can_access' => true,
            'reason' => null
        ];
    }

    /**
     * 初始化預設設定
     */
    public static function initializeDefaults(): void
    {
        // 本地系統設定初始化
        $defaults = [
            [
                'key' => self::MAINTENANCE_MODE,
                'value' => '0',
                'type' => self::TYPE_BOOLEAN,
                'category' => self::CATEGORY_GENERAL,
                'description' => '系統維護模式開關',
            ],
            [
                'key' => self::REMINDER_COOLDOWN_HOURS,
                'value' => '24',
                'type' => self::TYPE_INTEGER,
                'category' => self::CATEGORY_TIMING,
                'description' => '提醒郵件冷卻時間(小時)，限制考生在提交推薦人後的提醒頻率',
            ],
            [
                'key' => self::AUTO_TIMEOUT_DAYS,
                'value' => '7',
                'type' => self::TYPE_INTEGER,
                'category' => self::CATEGORY_TIMING,
                'description' => '自動超時天數(天)，超過此天數未提交的推薦人將被自動標記為超時，並透過系統排程檢查寄發通知信',
            ],
            [
                'key' => self::ALLOW_PDF_UPLOAD,
                'value' => '1',
                'type' => self::TYPE_BOOLEAN,
                'category' => self::CATEGORY_GENERAL,
                'description' => '是否允許推薦人上傳PDF檔案(0=不允許, 1=允許)',
            ],
            [
                'key' => self::ALLOW_QUESTIONNAIRE_SUBMISSION,
                'value' => '0',
                'type' => self::TYPE_BOOLEAN,
                'category' => self::CATEGORY_GENERAL,
                'description' => '允許推薦人填寫線上問卷(0=不允許, 1=允許)',
            ],
            [
                'key' => self::SYSTEM_SUPPORT_TEL,
                'value' => '0800-123-456',
                'type' => self::TYPE_STRING,
                'category' => self::CATEGORY_GENERAL,
                'description' => '系統支援電話(用於前台顯示)',
            ],
            [
                'key' => self::SYSTEM_SUPPORT_EMAIL,
                'value' => '<EMAIL>',
                'type' => self::TYPE_STRING,
                'category' => self::CATEGORY_GENERAL,
                'description' => '系統支援信箱(用於前台顯示)',
            ],
            [
                'key' => self::SYSTEM_ADMIN_EMAIL,
                'value' => '<EMAIL>',
                'type' => self::TYPE_STRING,
                'category' => self::CATEGORY_GENERAL,
                'description' => '系統管理員信箱(用於後台顯示)',
            ],
        ];

        // 從外部系統同步初始資料
        try {
            $syncService = new \App\Services\ExternalApiSyncService();
            $syncResult = $syncService->syncSystemSettings();

            if ($syncResult['success']) {
                Log::info('系統初始化時成功同步外部資料', $syncResult);
            } else {
                Log::warning('系統初始化時同步外部資料失敗，使用預設值', $syncResult);
            }
        } catch (\Exception $e) {
            Log::error('系統初始化同步失敗', ['error' => $e->getMessage()]);
        }

        foreach ($defaults as $default) {
            self::firstOrCreate(
                ['key' => $default['key']],
                $default
            );
        }
    }
}
