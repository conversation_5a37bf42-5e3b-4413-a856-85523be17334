<?php

namespace App\Http\Middleware;

use App\Models\SystemSetting;
use App\Models\SystemLog;
use App\Models\User;
use App\Models\Applicant;
use App\Models\Recommender;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;


/**
 * 考試時序檢查中間件
 * 
 * 檢查考試開放時間，用於控制用戶的操作限制
 */
class CheckExamTiming
{
    /**
     * 處理傳入的請求
     *
     * @param Request $request
     * @param Closure $next
     * @param string ...$permissions 需要檢查的權限
     * @return Response
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        // 管理員不受時序限制
        if ($user && $user->isAdmin()) {
            return $next($request);
        }

        // 檢查用戶特定考試期間限制
        if (in_array('user_exam_period', $permissions) && $user) {
            $examPeriodCheck = $this->checkUserExamPeriod($user);

            if (!$examPeriodCheck['can_access']) {
                SystemLog::logOperation(
                    SystemLog::ACTION_VIEW,
                    '嘗試在非考試期間訪問',
                    [
                        'user_id' => $user->id,
                        'user_role' => $user->role,
                        'exam_id' => $examPeriodCheck['exam_id'] ?? null,
                        'exam_year' => $examPeriodCheck['exam_year'] ?? null,
                        'reason' => $examPeriodCheck['reason'],
                        'requested_url' => $request->url(),
                    ],
                    SystemLog::LEVEL_WARNING
                );

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => '您的考試期間尚未開放或已結束',
                        'error_code' => 'USER_EXAM_PERIOD_CLOSED'
                    ], 403);
                }

                return back()->withErrors([
                    'system' => '您的考試期間尚未開放或已結束'
                ]);
            }
        }

        Log::info('【Middleware】系統時序檢查通過', [
            'user_id' => $user ? $user->id : null,
            'requested_url' => $request->url(),
            'permissions' => $permissions
        ]);

        return $next($request);
    }

    /**
     * 檢查用戶的考試期間是否開放
     *
     * @param User $user
     * @return array
     */
    private function checkUserExamPeriod(User $user): array
    {
        $examId = null;
        $examYear = null;

        // 根據用戶角色獲取考試資訊
        if ($user->isApplicant()) {
            $applicantId = Session::get('applicant_id');
            if ($applicantId) {
                $applicant = Applicant::find($applicantId);
                if ($applicant) {
                    $examId = $applicant->exam_id;
                    $examYear = $applicant->exam_year;
                }
            }
        } elseif ($user->isRecommender()) {
            $recommenderId = Session::get('recommender_id');
            if ($recommenderId) {
                $recommender = Recommender::find($recommenderId);
                if ($recommender) {
                    $examId = $recommender->exam_id;
                    $examYear = $recommender->exam_year;
                }
            } else {
                // 如果session中沒有recommender_id，嘗試通過email查找
                $recommender = Recommender::where('email', $user->email)->first();
                if ($recommender) {
                    $examId = $recommender->exam_id;
                    $examYear = $recommender->exam_year;
                }
            }
        }

        return SystemSetting::canUserAccessByExamPeriod($examId, $examYear);
    }
}
