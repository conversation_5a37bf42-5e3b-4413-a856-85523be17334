<?php

namespace App\Http\Middleware;

use App\Models\SystemSetting;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

/**
 * 檢查系統存取權限中間件
 *
 * 根據招生時程和系統開放時間控制使用者存取權限
 * 管理員不受限制，其他使用者需要在招生期間且系統開放時才能存取
 */
class CheckSystemAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 檢查維護模式
        if (SystemSetting::isMaintenanceMode()) {
            // 如果使用者未登入或不是管理員，顯示維護頁面
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => '系統目前正在維護中，請稍後再試',
                        'error_code' => 'MAINTENANCE_MODE'
                    ], 503);
                }

                Auth::logout(); // 登出使用者

                return Inertia::render('Maintenance', [
                    'message' => '系統目前正在維護中，請稍後再試'
                ])->toResponse($request);
            }
        }

        // 如果使用者未登入，讓其他中間件處理
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();
        $accessCheck = SystemSetting::canUserAccessSystem($user->role);

        // 如果可以存取，繼續處理請求
        if ($accessCheck['can_access']) {
            return $next($request);
        }

        // 根據不同的限制原因顯示不同的錯誤頁面
        $errorData = $this->getErrorData($accessCheck['reason']);

        // 如果是 API 請求，返回 JSON 錯誤
        if ($request->expectsJson()) {
            return response()->json([
                'error' => true,
                'message' => $errorData['message'],
                'reason' => $accessCheck['reason']
            ], 403);
        }

        // 返回錯誤頁面
        return Inertia::render('errors/SystemAccessDenied', $errorData)
            ->toResponse($request)
            ->setStatusCode(403);
    }

    /**
     * 根據限制原因取得錯誤資料
     */
    private function getErrorData(string $reason): array
    {
        $recruitmentPeriod = SystemSetting::getRecruitmentPeriod();

        switch ($reason) {
            case 'system_closed':
                return [
                    'title' => '系統暫時關閉',
                    'message' => '推薦函系統目前暫時關閉，請稍後再試。',
                    'icon' => 'lock',
                    'recruitment_period' => $recruitmentPeriod,
                ];

            case 'not_in_recruitment_period':
                return [
                    'title' => '非招生期間',
                    'message' => '目前不在招生期間內，推薦函系統暫時無法使用。',
                    'icon' => 'calendar',
                    'recruitment_period' => $recruitmentPeriod,
                ];

            default:
                return [
                    'title' => '存取被拒絕',
                    'message' => '您目前無法存取此系統。',
                    'icon' => 'shield',
                    'recruitment_period' => $recruitmentPeriod,
                ];
        }
    }
}
