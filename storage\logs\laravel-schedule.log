
   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 10:35:09 Running ['artisan' system:sync-recruitment-periods] in background  460.47ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods > '/dev/null' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 10:40:09 Running ['artisan' system:sync-recruitment-periods] in background  694.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods > 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 10:45:09 Running ['artisan' system:sync-recruitment-periods] in background  112.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods > 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 10:50:08 Running ['artisan' system:sync-recruitment-periods] in background  90.89ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods > 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 10:55:09 Running ['artisan' system:sync-recruitment-periods] in background  97.49ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 11:00:09 Running ['artisan' recommendations:update-status] in background  104.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-4e233a8f1e3a42dccedc01d0177a003eaf472b12" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:00:09 Running ['artisan' system:sync-recruitment-periods] in background  16.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 11:05:08 Running ['artisan' system:sync-recruitment-periods] in background  99.82ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 11:08:20 Running ['artisan' recommendations:update-status] in background  141.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:09:12 Running ['artisan' recommendations:update-status] in background  102.44ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:10:13 Running ['artisan' recommendations:update-status] in background  93.71ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:10:13 Running ['artisan' recommendations:send-reminders --dry-run] in background  22.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:10:14 Running ['artisan' system:sync-recruitment-periods] in background  12.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:11:10 Running ['artisan' recommendations:update-status] in background  115.00ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:11:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:11:10 Running ['artisan' emails:retry-failed --dry-run] in background  19.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:12:08 Running ['artisan' recommendations:update-status] in background  131.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:12:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  68.77ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:12:09 Running ['artisan' emails:retry-failed --dry-run] in background  20.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:13:09 Running ['artisan' recommendations:update-status] in background  163.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:13:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  33.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:13:10 Running ['artisan' emails:retry-failed --dry-run] in background  69.68ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:14:13 Running ['artisan' recommendations:update-status] in background  133.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:14:13 Running ['artisan' recommendations:send-reminders --dry-run] in background  54.56ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:14:13 Running ['artisan' emails:retry-failed --dry-run] in background  17.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:15:10 Running ['artisan' recommendations:update-status] in background  92.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-104e02b085b8a7285d59ed549738d4da982f6f97" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:15:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  25.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:15:10 Running ['artisan' emails:retry-failed --dry-run] in background  17.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:15:10 Running ['artisan' system:sync-recruitment-periods] in background  19.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:16:12 Running ['artisan' recommendations:update-status --dry-run] in background  113.17ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:16:12 Running ['artisan' recommendations:send-reminders --dry-run] in background  19.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:16:12 Running ['artisan' emails:retry-failed --dry-run] in background  22.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:17:11 Running ['artisan' recommendations:update-status --dry-run] in background  93.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:17:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:17:11 Running ['artisan' emails:retry-failed --dry-run] in background  20.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:18:10 Running ['artisan' recommendations:update-status --dry-run] in background  101.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:18:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.66ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:18:11 Running ['artisan' emails:retry-failed --dry-run] in background  15.90ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:19:11 Running ['artisan' recommendations:update-status --dry-run] in background  110.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:19:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  40.93ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:19:11 Running ['artisan' emails:retry-failed --dry-run] in background  21.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:19:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:19:11 Running ['artisan' system:sync-recruitment-periods] in background  34.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:20:08 Running ['artisan' recommendations:update-status --dry-run] in background  182.59ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:20:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  22.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:20:08 Running ['artisan' emails:retry-failed --dry-run] in background  16.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:20:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:20:08 Running ['artisan' system:sync-recruitment-periods] in background  14.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:21:07 Running ['artisan' recommendations:update-status --dry-run] in background  92.47ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:21:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  21.32ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:21:07 Running ['artisan' emails:retry-failed --dry-run] in background  17.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:21:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:21:07 Running ['artisan' system:sync-recruitment-periods] in background  13.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:22:09 Running ['artisan' recommendations:update-status --dry-run] in background  99.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:22:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.14ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:22:09 Running ['artisan' emails:retry-failed --dry-run] in background  15.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:22:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:22:09 Running ['artisan' system:sync-recruitment-periods] in background  15.74ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:23:09 Running ['artisan' recommendations:update-status --dry-run] in background  102.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:23:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:23:09 Running ['artisan' emails:retry-failed --dry-run] in background  12.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:23:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:23:09 Running ['artisan' system:sync-recruitment-periods] in background  17.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:24:09 Running ['artisan' recommendations:update-status --dry-run] in background  87.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:24:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:24:09 Running ['artisan' emails:retry-failed --dry-run] in background  18.34ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:24:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:24:09 Running ['artisan' system:sync-recruitment-periods] in background  17.59ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:25:10 Running ['artisan' recommendations:update-status --dry-run] in background  82.51ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:25:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.82ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:25:10 Running ['artisan' emails:retry-failed --dry-run] in background  12.00ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:25:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  26.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:25:10 Running ['artisan' system:sync-recruitment-periods] in background  22.17ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:26:09 Running ['artisan' recommendations:update-status --dry-run] in background  90.19ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:26:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:26:09 Running ['artisan' emails:retry-failed --dry-run] in background  11.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:26:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.82ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:26:09 Running ['artisan' system:sync-recruitment-periods] in background  21.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:27:08 Running ['artisan' recommendations:update-status --dry-run] in background  -973.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:27:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:27:07 Running ['artisan' emails:retry-failed --dry-run] in background  19.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:27:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  22.59ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:27:08 Running ['artisan' system:sync-recruitment-periods] in background  22.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:28:07 Running ['artisan' recommendations:update-status --dry-run] in background  88.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:28:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:28:08 Running ['artisan' emails:retry-failed --dry-run] in background  13.32ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:28:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  28.94ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:28:08 Running ['artisan' system:sync-recruitment-periods] in background  22.60ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:29:07 Running ['artisan' recommendations:update-status --dry-run] in background  101.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:29:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.24ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:29:08 Running ['artisan' emails:retry-failed --dry-run] in background  17.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:29:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:29:08 Running ['artisan' system:sync-recruitment-periods] in background  20.68ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:30:09 Running ['artisan' recommendations:update-status --dry-run] in background  82.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:30:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:30:10 Running ['artisan' emails:retry-failed --dry-run] in background  26.36ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:30:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:30:10 Running ['artisan' system:sync-recruitment-periods] in background  14.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:31:09 Running ['artisan' recommendations:update-status --dry-run] in background  80.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:31:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:31:10 Running ['artisan' emails:retry-failed --dry-run] in background  18.57ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:31:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:31:10 Running ['artisan' system:sync-recruitment-periods] in background  17.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:32:09 Running ['artisan' recommendations:update-status --dry-run] in background  78.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:32:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.12ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:32:09 Running ['artisan' emails:retry-failed --dry-run] in background  12.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:32:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:32:10 Running ['artisan' system:sync-recruitment-periods] in background  23.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:33:09 Running ['artisan' recommendations:update-status --dry-run] in background  95.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:33:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:33:09 Running ['artisan' emails:retry-failed --dry-run] in background  20.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:33:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:33:09 Running ['artisan' system:sync-recruitment-periods] in background  13.89ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:34:09 Running ['artisan' recommendations:update-status --dry-run] in background  109.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:34:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:34:10 Running ['artisan' emails:retry-failed --dry-run] in background  17.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:34:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  28.22ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:34:10 Running ['artisan' system:sync-recruitment-periods] in background  14.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:35:07 Running ['artisan' recommendations:update-status --dry-run] in background  81.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:35:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.67ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:35:08 Running ['artisan' emails:retry-failed --dry-run] in background  14.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:35:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:35:08 Running ['artisan' system:sync-recruitment-periods] in background  13.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:36:07 Running ['artisan' recommendations:update-status --dry-run] in background  81.00ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:36:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:36:08 Running ['artisan' emails:retry-failed --dry-run] in background  44.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:36:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:36:08 Running ['artisan' system:sync-recruitment-periods] in background  12.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:37:09 Running ['artisan' recommendations:update-status --dry-run] in background  81.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:37:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  8.77ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:37:09 Running ['artisan' emails:retry-failed --dry-run] in background  15.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:37:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:37:10 Running ['artisan' system:sync-recruitment-periods] in background  20.71ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:38:09 Running ['artisan' recommendations:update-status --dry-run] in background  89.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:38:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  18.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:38:09 Running ['artisan' emails:retry-failed --dry-run] in background  15.36ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:38:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.57ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:38:09 Running ['artisan' system:sync-recruitment-periods] in background  13.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:39:09 Running ['artisan' recommendations:update-status --dry-run] in background  108.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:39:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  18.84ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:39:09 Running ['artisan' emails:retry-failed --dry-run] in background  16.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:39:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.85ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:39:09 Running ['artisan' system:sync-recruitment-periods] in background  13.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:40:09 Running ['artisan' recommendations:update-status --dry-run] in background  81.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:40:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  26.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:40:10 Running ['artisan' emails:retry-failed --dry-run] in background  20.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:40:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:40:10 Running ['artisan' system:sync-recruitment-periods] in background  13.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:41:09 Running ['artisan' recommendations:update-status --dry-run] in background  88.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:41:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:41:09 Running ['artisan' emails:retry-failed --dry-run] in background  13.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:41:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.57ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:41:10 Running ['artisan' system:sync-recruitment-periods] in background  28.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:42:09 Running ['artisan' recommendations:update-status --dry-run] in background  93.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:42:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:42:09 Running ['artisan' emails:retry-failed --dry-run] in background  22.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:42:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  16.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:42:09 Running ['artisan' system:sync-recruitment-periods] in background  11.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:43:07 Running ['artisan' recommendations:update-status --dry-run] in background  81.67ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:43:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:43:07 Running ['artisan' emails:retry-failed --dry-run] in background  10.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:43:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:43:07 Running ['artisan' system:sync-recruitment-periods] in background  17.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:44:06 Running ['artisan' recommendations:update-status --dry-run] in background  119.89ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:44:06 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:44:06 Running ['artisan' emails:retry-failed --dry-run] in background  19.78ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:44:06 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:44:06 Running ['artisan' system:sync-recruitment-periods] in background  12.76ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:45:09 Running ['artisan' recommendations:update-status --dry-run] in background  79.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:45:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.97ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:45:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:45:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  24.32ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:45:09 Running ['artisan' system:sync-recruitment-periods] in background  17.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:46:09 Running ['artisan' recommendations:update-status --dry-run] in background  80.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:46:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:46:09 Running ['artisan' emails:retry-failed --dry-run] in background  23.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:46:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  31.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:46:09 Running ['artisan' system:sync-recruitment-periods] in background  15.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:47:10 Running ['artisan' recommendations:update-status --dry-run] in background  98.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:47:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  20.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:47:10 Running ['artisan' emails:retry-failed --dry-run] in background  10.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:47:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:47:10 Running ['artisan' system:sync-recruitment-periods] in background  23.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:48:09 Running ['artisan' recommendations:update-status --dry-run] in background  102.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:48:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.11ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:48:09 Running ['artisan' emails:retry-failed --dry-run] in background  11.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:48:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:48:09 Running ['artisan' system:sync-recruitment-periods] in background  20.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:49:10 Running ['artisan' recommendations:update-status --dry-run] in background  91.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:49:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  18.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:49:11 Running ['artisan' emails:retry-failed --dry-run] in background  26.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:49:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:49:11 Running ['artisan' system:sync-recruitment-periods] in background  15.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:50:08 Running ['artisan' recommendations:update-status --dry-run] in background  86.77ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:50:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:50:08 Running ['artisan' emails:retry-failed --dry-run] in background  14.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:50:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:50:08 Running ['artisan' system:sync-recruitment-periods] in background  13.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:51:06 Running ['artisan' recommendations:update-status --dry-run] in background  88.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:51:06 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:51:06 Running ['artisan' emails:retry-failed --dry-run] in background  11.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:51:06 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:51:06 Running ['artisan' system:sync-recruitment-periods] in background  18.57ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:52:08 Running ['artisan' recommendations:update-status --dry-run] in background  95.24ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:52:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:52:08 Running ['artisan' emails:retry-failed --dry-run] in background  10.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:52:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:52:08 Running ['artisan' system:sync-recruitment-periods] in background  19.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:53:09 Running ['artisan' recommendations:update-status --dry-run] in background  104.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:53:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.67ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:53:10 Running ['artisan' emails:retry-failed --dry-run] in background  11.68ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:53:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:53:10 Running ['artisan' system:sync-recruitment-periods] in background  20.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:54:09 Running ['artisan' recommendations:update-status --dry-run] in background  86.14ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:54:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  18.19ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:54:09 Running ['artisan' emails:retry-failed --dry-run] in background  12.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:54:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  10.90ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:54:09 Running ['artisan' system:sync-recruitment-periods] in background  13.17ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:55:09 Running ['artisan' recommendations:update-status --dry-run] in background  83.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:55:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:55:09 Running ['artisan' emails:retry-failed --dry-run] in background  25.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:55:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  26.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:55:10 Running ['artisan' system:sync-recruitment-periods] in background  28.78ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:56:09 Running ['artisan' recommendations:update-status --dry-run] in background  94.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:56:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  20.14ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:56:09 Running ['artisan' emails:retry-failed --dry-run] in background  13.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:56:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:56:09 Running ['artisan' system:sync-recruitment-periods] in background  12.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:57:09 Running ['artisan' recommendations:update-status --dry-run] in background  77.94ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:57:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:57:09 Running ['artisan' emails:retry-failed --dry-run] in background  19.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:57:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:57:09 Running ['artisan' system:sync-recruitment-periods] in background  25.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:58:07 Running ['artisan' recommendations:update-status --dry-run] in background  85.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:58:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:58:08 Running ['artisan' emails:retry-failed --dry-run] in background  15.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:58:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:58:08 Running ['artisan' system:sync-recruitment-periods] in background  13.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 11:59:07 Running ['artisan' recommendations:update-status --dry-run] in background  81.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:59:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:59:08 Running ['artisan' emails:retry-failed --dry-run] in background  18.51ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:59:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 11:59:08 Running ['artisan' system:sync-recruitment-periods] in background  18.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:00:09 Running ['artisan' recommendations:update-status --dry-run] in background  81.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:00:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:00:09 Running ['artisan' emails:retry-failed --dry-run] in background  16.44ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:00:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:00:10 Running ['artisan' system:sync-recruitment-periods] in background  11.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:01:09 Running ['artisan' recommendations:update-status --dry-run] in background  85.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:01:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:01:09 Running ['artisan' emails:retry-failed --dry-run] in background  22.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:01:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:01:10 Running ['artisan' system:sync-recruitment-periods] in background  30.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:02:09 Running ['artisan' recommendations:update-status --dry-run] in background  86.11ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:02:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:02:09 Running ['artisan' emails:retry-failed --dry-run] in background  18.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:02:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:02:09 Running ['artisan' system:sync-recruitment-periods] in background  17.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:03:09 Running ['artisan' recommendations:update-status --dry-run] in background  85.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:03:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:03:09 Running ['artisan' emails:retry-failed --dry-run] in background  24.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:03:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  29.84ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:03:09 Running ['artisan' system:sync-recruitment-periods] in background  16.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:04:08 Running ['artisan' recommendations:update-status --dry-run] in background  82.94ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:04:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:04:09 Running ['artisan' emails:retry-failed --dry-run] in background  21.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:04:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:04:09 Running ['artisan' system:sync-recruitment-periods] in background  16.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:05:07 Running ['artisan' recommendations:update-status --dry-run] in background  80.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:05:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.11ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:05:08 Running ['artisan' emails:retry-failed --dry-run] in background  16.94ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:05:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  24.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:05:08 Running ['artisan' system:sync-recruitment-periods] in background  31.99ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:06:07 Running ['artisan' recommendations:update-status --dry-run] in background  77.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:06:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:06:07 Running ['artisan' emails:retry-failed --dry-run] in background  15.67ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:06:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  23.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:06:08 Running ['artisan' system:sync-recruitment-periods] in background  15.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:07:09 Running ['artisan' recommendations:update-status --dry-run] in background  89.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:07:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:07:09 Running ['artisan' emails:retry-failed --dry-run] in background  12.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:07:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  10.89ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:07:09 Running ['artisan' system:sync-recruitment-periods] in background  17.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:08:09 Running ['artisan' recommendations:update-status --dry-run] in background  83.60ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:08:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  21.75ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:08:10 Running ['artisan' emails:retry-failed --dry-run] in background  12.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:08:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:08:10 Running ['artisan' system:sync-recruitment-periods] in background  16.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:09:09 Running ['artisan' recommendations:update-status --dry-run] in background  92.32ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:09:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:09:09 Running ['artisan' emails:retry-failed --dry-run] in background  11.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:09:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.75ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:09:09 Running ['artisan' system:sync-recruitment-periods] in background  14.51ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:10:09 Running ['artisan' recommendations:update-status --dry-run] in background  87.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:10:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:10:09 Running ['artisan' emails:retry-failed --dry-run] in background  13.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:10:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:10:09 Running ['artisan' system:sync-recruitment-periods] in background  17.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:11:09 Running ['artisan' recommendations:update-status --dry-run] in background  77.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:11:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  20.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:11:10 Running ['artisan' emails:retry-failed --dry-run] in background  18.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:11:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:11:10 Running ['artisan' system:sync-recruitment-periods] in background  13.74ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:12:09 Running ['artisan' recommendations:update-status --dry-run] in background  -701.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:12:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:12:08 Running ['artisan' emails:retry-failed --dry-run] in background  11.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:12:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  22.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:12:08 Running ['artisan' system:sync-recruitment-periods] in background  13.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:13:07 Running ['artisan' recommendations:update-status --dry-run] in background  86.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:13:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  23.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:13:07 Running ['artisan' emails:retry-failed --dry-run] in background  12.66ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:13:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:13:08 Running ['artisan' system:sync-recruitment-periods] in background  21.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:14:07 Running ['artisan' recommendations:update-status --dry-run] in background  79.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:14:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.99ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:14:07 Running ['artisan' emails:retry-failed --dry-run] in background  20.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:14:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  22.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:14:07 Running ['artisan' system:sync-recruitment-periods] in background  19.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:15:09 Running ['artisan' recommendations:update-status --dry-run] in background  80.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:15:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:15:09 Running ['artisan' emails:retry-failed --dry-run] in background  20.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:15:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  25.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:15:09 Running ['artisan' system:sync-recruitment-periods] in background  29.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:16:08 Running ['artisan' recommendations:update-status --dry-run] in background  89.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:16:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:16:09 Running ['artisan' emails:retry-failed --dry-run] in background  20.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:16:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  10.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:16:09 Running ['artisan' system:sync-recruitment-periods] in background  12.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:17:09 Running ['artisan' recommendations:update-status --dry-run] in background  80.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:17:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.60ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:17:09 Running ['artisan' emails:retry-failed --dry-run] in background  21.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:17:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.74ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:17:09 Running ['artisan' system:sync-recruitment-periods] in background  21.56ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:18:09 Running ['artisan' recommendations:update-status --dry-run] in background  102.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:18:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:18:09 Running ['artisan' emails:retry-failed --dry-run] in background  17.97ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:18:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  16.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:18:09 Running ['artisan' system:sync-recruitment-periods] in background  11.78ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:19:08 Running ['artisan' recommendations:update-status --dry-run] in background  84.94ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:19:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:19:09 Running ['artisan' emails:retry-failed --dry-run] in background  15.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:19:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:19:09 Running ['artisan' system:sync-recruitment-periods] in background  23.47ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:20:07 Running ['artisan' recommendations:update-status --dry-run] in background  89.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:20:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  16.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:20:08 Running ['artisan' emails:retry-failed --dry-run] in background  16.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:20:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.87ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:20:08 Running ['artisan' system:sync-recruitment-periods] in background  18.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:21:07 Running ['artisan' recommendations:update-status --dry-run] in background  78.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:21:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  24.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:21:08 Running ['artisan' emails:retry-failed --dry-run] in background  22.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:21:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:21:08 Running ['artisan' system:sync-recruitment-periods] in background  15.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:22:09 Running ['artisan' recommendations:update-status --dry-run] in background  81.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:22:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:22:09 Running ['artisan' emails:retry-failed --dry-run] in background  26.60ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:22:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.97ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:22:09 Running ['artisan' system:sync-recruitment-periods] in background  14.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:23:09 Running ['artisan' recommendations:update-status --dry-run] in background  103.36ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:23:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  21.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:23:09 Running ['artisan' emails:retry-failed --dry-run] in background  18.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:23:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:23:09 Running ['artisan' system:sync-recruitment-periods] in background  27.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:24:09 Running ['artisan' recommendations:update-status --dry-run] in background  77.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:24:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  20.32ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:24:09 Running ['artisan' emails:retry-failed --dry-run] in background  20.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:24:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  23.24ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:24:09 Running ['artisan' system:sync-recruitment-periods] in background  14.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:25:09 Running ['artisan' recommendations:update-status --dry-run] in background  104.78ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:25:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:25:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:25:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  23.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:25:09 Running ['artisan' system:sync-recruitment-periods] in background  17.66ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:26:09 Running ['artisan' recommendations:update-status --dry-run] in background  108.97ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:26:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:26:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:26:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:26:09 Running ['artisan' system:sync-recruitment-periods] in background  22.00ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:27:08 Running ['artisan' recommendations:update-status --dry-run] in background  87.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:27:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  26.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:27:07 Running ['artisan' emails:retry-failed --dry-run] in background  16.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:27:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:27:07 Running ['artisan' system:sync-recruitment-periods] in background  10.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:28:08 Running ['artisan' recommendations:update-status --dry-run] in background  82.77ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:28:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  18.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:28:08 Running ['artisan' emails:retry-failed --dry-run] in background  23.93ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:28:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  25.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:28:08 Running ['artisan' system:sync-recruitment-periods] in background  16.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:29:06 Running ['artisan' recommendations:update-status --dry-run] in background  107.45ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:29:06 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.08ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:29:06 Running ['artisan' emails:retry-failed --dry-run] in background  11.08ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:29:06 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:29:07 Running ['artisan' system:sync-recruitment-periods] in background  11.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:30:09 Running ['artisan' recommendations:update-status --dry-run] in background  86.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:30:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.44ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:30:09 Running ['artisan' emails:retry-failed --dry-run] in background  18.36ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:30:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  27.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:30:09 Running ['artisan' system:sync-recruitment-periods] in background  14.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:31:09 Running ['artisan' recommendations:update-status --dry-run] in background  80.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:31:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  16.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:31:10 Running ['artisan' emails:retry-failed --dry-run] in background  20.12ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:31:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  16.17ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:31:10 Running ['artisan' system:sync-recruitment-periods] in background  16.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:32:09 Running ['artisan' recommendations:update-status --dry-run] in background  88.21ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:32:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:32:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.56ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:32:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.21ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:32:09 Running ['artisan' system:sync-recruitment-periods] in background  11.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:33:09 Running ['artisan' recommendations:update-status --dry-run] in background  95.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:33:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  19.12ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:33:09 Running ['artisan' emails:retry-failed --dry-run] in background  25.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:33:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:33:09 Running ['artisan' system:sync-recruitment-periods] in background  11.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:34:09 Running ['artisan' recommendations:update-status --dry-run] in background  84.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:34:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:34:10 Running ['artisan' emails:retry-failed --dry-run] in background  9.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:34:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  -794.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:34:09 Running ['artisan' system:sync-recruitment-periods] in background  18.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:35:07 Running ['artisan' recommendations:update-status --dry-run] in background  78.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:35:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:35:07 Running ['artisan' emails:retry-failed --dry-run] in background  10.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:35:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:35:07 Running ['artisan' system:sync-recruitment-periods] in background  23.45ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:36:06 Running ['artisan' recommendations:update-status --dry-run] in background  95.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:36:06 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.60ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:36:06 Running ['artisan' emails:retry-failed --dry-run] in background  11.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:36:06 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:36:06 Running ['artisan' system:sync-recruitment-periods] in background  20.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:37:09 Running ['artisan' recommendations:update-status --dry-run] in background  87.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:37:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  21.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:37:10 Running ['artisan' emails:retry-failed --dry-run] in background  34.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:37:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:37:10 Running ['artisan' system:sync-recruitment-periods] in background  12.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:38:09 Running ['artisan' recommendations:update-status --dry-run] in background  89.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:38:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.82ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:38:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:38:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.14ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:38:10 Running ['artisan' system:sync-recruitment-periods] in background  22.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:39:09 Running ['artisan' recommendations:update-status --dry-run] in background  104.60ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:39:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:39:09 Running ['artisan' emails:retry-failed --dry-run] in background  17.99ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:39:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  16.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:39:09 Running ['artisan' system:sync-recruitment-periods] in background  17.51ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:40:09 Running ['artisan' recommendations:update-status --dry-run] in background  89.08ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:40:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.17ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:40:10 Running ['artisan' emails:retry-failed --dry-run] in background  11.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:40:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  10.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:40:10 Running ['artisan' system:sync-recruitment-periods] in background  14.68ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:41:09 Running ['artisan' recommendations:update-status --dry-run] in background  86.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:41:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.89ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:41:09 Running ['artisan' emails:retry-failed --dry-run] in background  24.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:41:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  29.97ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:41:10 Running ['artisan' system:sync-recruitment-periods] in background  22.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:42:07 Running ['artisan' recommendations:update-status --dry-run] in background  86.14ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:42:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:42:07 Running ['artisan' emails:retry-failed --dry-run] in background  10.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:42:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:42:07 Running ['artisan' system:sync-recruitment-periods] in background  21.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:43:07 Running ['artisan' recommendations:update-status --dry-run] in background  85.14ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:43:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  16.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:43:08 Running ['artisan' emails:retry-failed --dry-run] in background  16.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:43:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:43:08 Running ['artisan' system:sync-recruitment-periods] in background  13.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:44:09 Running ['artisan' recommendations:update-status --dry-run] in background  87.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:44:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.14ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:44:09 Running ['artisan' emails:retry-failed --dry-run] in background  16.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:44:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:44:09 Running ['artisan' system:sync-recruitment-periods] in background  13.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:45:08 Running ['artisan' recommendations:update-status --dry-run] in background  85.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:45:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.82ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:45:09 Running ['artisan' emails:retry-failed --dry-run] in background  11.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:45:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.36ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:45:09 Running ['artisan' system:sync-recruitment-periods] in background  13.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:46:09 Running ['artisan' recommendations:update-status --dry-run] in background  87.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:46:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:46:09 Running ['artisan' emails:retry-failed --dry-run] in background  13.45ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:46:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:46:09 Running ['artisan' system:sync-recruitment-periods] in background  20.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:47:09 Running ['artisan' recommendations:update-status --dry-run] in background  84.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:47:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:47:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.75ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:47:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.59ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:47:09 Running ['artisan' system:sync-recruitment-periods] in background  20.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:48:09 Running ['artisan' recommendations:update-status --dry-run] in background  79.71ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:48:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  23.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:48:10 Running ['artisan' emails:retry-failed --dry-run] in background  19.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:48:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  29.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:48:10 Running ['artisan' system:sync-recruitment-periods] in background  18.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:49:07 Running ['artisan' recommendations:update-status --dry-run] in background  84.85ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:49:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  23.57ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:49:07 Running ['artisan' emails:retry-failed --dry-run] in background  24.00ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:49:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  22.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:49:08 Running ['artisan' system:sync-recruitment-periods] in background  17.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:50:07 Running ['artisan' recommendations:update-status --dry-run] in background  98.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:50:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.89ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:50:07 Running ['artisan' emails:retry-failed --dry-run] in background  17.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:50:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  27.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:50:07 Running ['artisan' system:sync-recruitment-periods] in background  12.24ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:51:09 Running ['artisan' recommendations:update-status --dry-run] in background  79.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:51:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:51:10 Running ['artisan' emails:retry-failed --dry-run] in background  22.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:51:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:51:10 Running ['artisan' system:sync-recruitment-periods] in background  18.47ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:52:09 Running ['artisan' recommendations:update-status --dry-run] in background  87.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:52:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:52:09 Running ['artisan' emails:retry-failed --dry-run] in background  11.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:52:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:52:09 Running ['artisan' system:sync-recruitment-periods] in background  14.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:53:09 Running ['artisan' recommendations:update-status --dry-run] in background  103.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:53:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  16.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:53:09 Running ['artisan' emails:retry-failed --dry-run] in background  21.22ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:53:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:53:09 Running ['artisan' system:sync-recruitment-periods] in background  12.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:54:09 Running ['artisan' recommendations:update-status --dry-run] in background  86.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:54:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:54:10 Running ['artisan' emails:retry-failed --dry-run] in background  10.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:54:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:54:10 Running ['artisan' system:sync-recruitment-periods] in background  13.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:55:09 Running ['artisan' recommendations:update-status --dry-run] in background  87.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:55:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  31.77ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:55:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:55:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:55:09 Running ['artisan' system:sync-recruitment-periods] in background  15.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:56:08 Running ['artisan' recommendations:update-status --dry-run] in background  84.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:56:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  16.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:56:08 Running ['artisan' emails:retry-failed --dry-run] in background  13.34ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:56:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  12.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:56:08 Running ['artisan' system:sync-recruitment-periods] in background  42.78ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:57:07 Running ['artisan' recommendations:update-status --dry-run] in background  86.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:57:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  16.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:57:07 Running ['artisan' emails:retry-failed --dry-run] in background  11.34ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:57:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:57:07 Running ['artisan' system:sync-recruitment-periods] in background  11.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:58:05 Running ['artisan' recommendations:update-status --dry-run] in background  92.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:58:06 Running ['artisan' recommendations:send-reminders --dry-run] in background  18.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:58:06 Running ['artisan' emails:retry-failed --dry-run] in background  16.34ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:58:06 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.90ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:58:06 Running ['artisan' system:sync-recruitment-periods] in background  12.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 12:59:09 Running ['artisan' recommendations:update-status --dry-run] in background  89.75ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:59:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.74ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:59:10 Running ['artisan' emails:retry-failed --dry-run] in background  15.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:59:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  10.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 12:59:10 Running ['artisan' system:sync-recruitment-periods] in background  13.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:00:09 Running ['artisan' recommendations:update-status --dry-run] in background  89.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:00:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  22.34ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:00:09 Running ['artisan' emails:retry-failed --dry-run] in background  22.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:00:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:00:09 Running ['artisan' system:sync-recruitment-periods] in background  14.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:01:09 Running ['artisan' recommendations:update-status --dry-run] in background  135.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:01:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  21.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:01:10 Running ['artisan' emails:retry-failed --dry-run] in background  18.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:01:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:01:10 Running ['artisan' system:sync-recruitment-periods] in background  20.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:02:09 Running ['artisan' recommendations:update-status --dry-run] in background  83.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:02:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.87ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:02:10 Running ['artisan' emails:retry-failed --dry-run] in background  13.57ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:02:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:02:10 Running ['artisan' system:sync-recruitment-periods] in background  17.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:03:09 Running ['artisan' recommendations:update-status --dry-run] in background  80.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:03:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:03:09 Running ['artisan' emails:retry-failed --dry-run] in background  17.11ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:03:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:03:09 Running ['artisan' system:sync-recruitment-periods] in background  13.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:04:07 Running ['artisan' recommendations:update-status --dry-run] in background  84.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:04:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:04:07 Running ['artisan' emails:retry-failed --dry-run] in background  17.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:04:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  16.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:04:07 Running ['artisan' system:sync-recruitment-periods] in background  17.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:05:07 Running ['artisan' recommendations:update-status --dry-run] in background  83.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:05:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:05:08 Running ['artisan' emails:retry-failed --dry-run] in background  17.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:05:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:05:08 Running ['artisan' system:sync-recruitment-periods] in background  18.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:06:09 Running ['artisan' recommendations:update-status --dry-run] in background  99.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:06:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:06:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:06:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:06:09 Running ['artisan' system:sync-recruitment-periods] in background  23.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:07:09 Running ['artisan' recommendations:update-status --dry-run] in background  84.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:07:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:07:10 Running ['artisan' emails:retry-failed --dry-run] in background  18.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:07:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  16.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:07:10 Running ['artisan' system:sync-recruitment-periods] in background  19.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:08:09 Running ['artisan' recommendations:update-status --dry-run] in background  84.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:08:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  36.56ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:08:09 Running ['artisan' emails:retry-failed --dry-run] in background  11.29ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:08:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:08:09 Running ['artisan' system:sync-recruitment-periods] in background  13.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:09:09 Running ['artisan' recommendations:update-status --dry-run] in background  89.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:09:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:09:10 Running ['artisan' emails:retry-failed --dry-run] in background  27.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:09:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:09:10 Running ['artisan' system:sync-recruitment-periods] in background  19.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:10:10 Running ['artisan' recommendations:update-status --dry-run] in background  87.02ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:10:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.49ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:10:09 Running ['artisan' emails:retry-failed --dry-run] in background  12.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:10:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.49ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:10:09 Running ['artisan' system:sync-recruitment-periods] in background  17.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:11:07 Running ['artisan' recommendations:update-status --dry-run] in background  80.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:11:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.21ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:11:07 Running ['artisan' emails:retry-failed --dry-run] in background  13.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:11:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.66ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:11:07 Running ['artisan' system:sync-recruitment-periods] in background  20.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:12:06 Running ['artisan' recommendations:update-status --dry-run] in background  122.44ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:12:06 Running ['artisan' recommendations:send-reminders --dry-run] in background  19.25ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:12:06 Running ['artisan' emails:retry-failed --dry-run] in background  21.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:12:06 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:12:07 Running ['artisan' system:sync-recruitment-periods] in background  11.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:13:09 Running ['artisan' recommendations:update-status --dry-run] in background  80.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:13:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.24ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:13:09 Running ['artisan' emails:retry-failed --dry-run] in background  18.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:13:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:13:09 Running ['artisan' system:sync-recruitment-periods] in background  21.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:14:09 Running ['artisan' recommendations:update-status --dry-run] in background  91.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:14:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:14:10 Running ['artisan' emails:retry-failed --dry-run] in background  12.93ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:14:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  30.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:14:10 Running ['artisan' system:sync-recruitment-periods] in background  13.50ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:15:09 Running ['artisan' recommendations:update-status --dry-run] in background  132.12ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:15:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:15:10 Running ['artisan' emails:retry-failed --dry-run] in background  18.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:15:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.45ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:15:10 Running ['artisan' system:sync-recruitment-periods] in background  13.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


  2025-08-07 13:16:09 Running ['artisan' recommendations:update-status --dry-run] in background  104.47ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-782fee2fdc2172d4d0265366ad08b0694982dc08" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:16:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-c154412a62a798c7ad1bf1bbb9ed7fc6c035cb77" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:16:09 Running ['artisan' emails:retry-failed --dry-run] in background  10.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0fbd4a3517bed0236afacca330c2de1a2375cb88" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:16:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  11.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-625c51bb187f848d5d7a79f32c71b9c57481af7e" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:16:09 Running ['artisan' system:sync-recruitment-periods] in background  22.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-264843dc9008a8dc43198624df524c9928acf656" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:20:06 Running ['artisan' recommendations:update-status --dry-run] in background  83.08ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:20:06 Running ['artisan' recommendations:send-reminders --dry-run] in background  9.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:20:06 Running ['artisan' emails:retry-failed --dry-run] in background  17.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:20:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:20:07 Running ['artisan' system:sync-recruitment-periods] in background  17.97ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:25:10 Running ['artisan' recommendations:update-status --dry-run] in background  80.66ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:25:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:25:10 Running ['artisan' emails:retry-failed --dry-run] in background  20.76ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:25:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:25:10 Running ['artisan' system:sync-recruitment-periods] in background  12.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:30:11 Running ['artisan' recommendations:update-status --dry-run] in background  108.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:30:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  15.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:30:11 Running ['artisan' emails:retry-failed --dry-run] in background  11.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:30:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:30:12 Running ['artisan' system:sync-recruitment-periods] in background  27.86ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:35:10 Running ['artisan' recommendations:update-status --dry-run] in background  97.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:35:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:35:10 Running ['artisan' emails:retry-failed --dry-run] in background  13.76ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:35:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:35:10 Running ['artisan' system:sync-recruitment-periods] in background  41.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:40:10 Running ['artisan' recommendations:update-status --dry-run] in background  93.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:40:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:40:10 Running ['artisan' emails:retry-failed --dry-run] in background  16.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:40:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:40:10 Running ['artisan' system:sync-recruitment-periods] in background  21.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:45:10 Running ['artisan' recommendations:update-status --dry-run] in background  98.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:45:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.22ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:45:11 Running ['artisan' emails:retry-failed --dry-run] in background  22.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:45:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  23.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:45:11 Running ['artisan' system:sync-recruitment-periods] in background  61.78ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:50:09 Running ['artisan' recommendations:update-status --dry-run] in background  91.45ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:50:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  19.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:50:10 Running ['artisan' emails:retry-failed --dry-run] in background  19.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:50:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.60ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:50:10 Running ['artisan' system:sync-recruitment-periods] in background  17.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 13:55:08 Running ['artisan' recommendations:update-status --dry-run] in background  87.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:55:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.12ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:55:09 Running ['artisan' emails:retry-failed --dry-run] in background  13.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:55:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  34.08ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 13:55:09 Running ['artisan' system:sync-recruitment-periods] in background  36.67ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:00:10 Running ['artisan' recommendations:update-status --dry-run] in background  82.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:00:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.92ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:00:10 Running ['artisan' emails:retry-failed --dry-run] in background  21.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:00:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  17.95ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:00:11 Running ['artisan' system:sync-recruitment-periods] in background  13.77ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:05:09 Running ['artisan' recommendations:update-status --dry-run] in background  82.87ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:05:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  29.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:05:10 Running ['artisan' emails:retry-failed --dry-run] in background  14.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:05:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  14.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:05:10 Running ['artisan' system:sync-recruitment-periods] in background  18.34ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:10:10 Running ['artisan' recommendations:update-status --dry-run] in background  85.36ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:10:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.05ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:10:11 Running ['artisan' emails:retry-failed --dry-run] in background  13.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:10:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:10:11 Running ['artisan' system:sync-recruitment-periods] in background  19.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:15:08 Running ['artisan' recommendations:update-status --dry-run] in background  113.62ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:15:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  16.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:15:09 Running ['artisan' emails:retry-failed --dry-run] in background  36.90ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:15:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.17ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:15:09 Running ['artisan' system:sync-recruitment-periods] in background  26.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:20:10 Running ['artisan' recommendations:update-status --dry-run] in background  105.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:20:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:20:11 Running ['artisan' emails:retry-failed --dry-run] in background  29.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:20:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:20:11 Running ['artisan' system:sync-recruitment-periods] in background  17.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:25:10 Running ['artisan' recommendations:update-status --dry-run] in background  126.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:25:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  42.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:25:11 Running ['artisan' emails:retry-failed --dry-run] in background  18.06ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:25:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  29.74ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:25:11 Running ['artisan' system:sync-recruitment-periods] in background  24.44ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:30:08 Running ['artisan' recommendations:update-status --dry-run] in background  105.76ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:30:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  23.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:30:09 Running ['artisan' emails:retry-failed --dry-run] in background  27.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:30:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  23.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:30:09 Running ['artisan' system:sync-recruitment-periods] in background  31.71ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:35:09 Running ['artisan' recommendations:update-status --dry-run] in background  99.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:35:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  21.32ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:35:09 Running ['artisan' emails:retry-failed --dry-run] in background  19.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:35:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  16.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:35:09 Running ['artisan' system:sync-recruitment-periods] in background  29.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:40:11 Running ['artisan' recommendations:update-status --dry-run] in background  103.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:40:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  20.91ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:40:11 Running ['artisan' emails:retry-failed --dry-run] in background  13.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:40:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:40:11 Running ['artisan' system:sync-recruitment-periods] in background  15.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:45:07 Running ['artisan' recommendations:update-status --dry-run] in background  81.68ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:45:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.49ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:45:08 Running ['artisan' emails:retry-failed --dry-run] in background  37.47ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:45:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:45:08 Running ['artisan' system:sync-recruitment-periods] in background  23.87ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:50:08 Running ['artisan' recommendations:update-status --dry-run] in background  -1,121.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:50:07 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.08ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:50:07 Running ['artisan' emails:retry-failed --dry-run] in background  13.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:50:07 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  24.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:50:08 Running ['artisan' system:sync-recruitment-periods] in background  33.61ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 14:55:10 Running ['artisan' recommendations:update-status --dry-run] in background  92.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:55:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.74ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:55:11 Running ['artisan' emails:retry-failed --dry-run] in background  13.94ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:55:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  19.90ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 14:55:11 Running ['artisan' system:sync-recruitment-periods] in background  18.98ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:00:10 Running ['artisan' recommendations:update-status --dry-run] in background  87.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:00:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  10.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:00:10 Running ['artisan' emails:retry-failed --dry-run] in background  12.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:00:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.59ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:00:10 Running ['artisan' system:sync-recruitment-periods] in background  38.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:05:09 Running ['artisan' recommendations:update-status --dry-run] in background  103.44ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:05:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:05:09 Running ['artisan' emails:retry-failed --dry-run] in background  19.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:05:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  26.51ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:05:09 Running ['artisan' system:sync-recruitment-periods] in background  19.44ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:10:11 Running ['artisan' recommendations:update-status --dry-run] in background  104.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:10:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  22.93ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:10:11 Running ['artisan' emails:retry-failed --dry-run] in background  23.88ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:10:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:10:11 Running ['artisan' system:sync-recruitment-periods] in background  28.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:15:12 Running ['artisan' recommendations:update-status --dry-run] in background  105.70ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:15:13 Running ['artisan' recommendations:send-reminders --dry-run] in background  17.17ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:15:13 Running ['artisan' emails:retry-failed --dry-run] in background  21.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:15:13 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  40.87ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:15:13 Running ['artisan' system:sync-recruitment-periods] in background  39.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:20:23 Running ['artisan' recommendations:update-status --dry-run] in background  141.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:20:23 Running ['artisan' recommendations:send-reminders --dry-run] in background  26.47ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:20:23 Running ['artisan' emails:retry-failed --dry-run] in background  61.99ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:20:24 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  49.22ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:20:24 Running ['artisan' system:sync-recruitment-periods] in background  27.65ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:25:16 Running ['artisan' recommendations:update-status --dry-run] in background  141.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:25:16 Running ['artisan' recommendations:send-reminders --dry-run] in background  19.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:25:16 Running ['artisan' emails:retry-failed --dry-run] in background  32.54ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:25:16 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  36.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:25:16 Running ['artisan' system:sync-recruitment-periods] in background  41.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:30:10 Running ['artisan' recommendations:update-status --dry-run] in background  93.53ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:30:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  30.21ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:30:11 Running ['artisan' emails:retry-failed --dry-run] in background  14.87ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:30:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  38.96ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:30:11 Running ['artisan' system:sync-recruitment-periods] in background  29.58ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:35:09 Running ['artisan' recommendations:update-status --dry-run] in background  102.52ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:35:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  18.20ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:35:10 Running ['artisan' emails:retry-failed --dry-run] in background  29.11ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:35:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  13.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:35:10 Running ['artisan' system:sync-recruitment-periods] in background  12.80ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:40:08 Running ['artisan' recommendations:update-status --dry-run] in background  87.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:40:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:40:08 Running ['artisan' emails:retry-failed --dry-run] in background  19.43ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:40:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  22.75ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:40:08 Running ['artisan' system:sync-recruitment-periods] in background  13.46ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:45:08 Running ['artisan' recommendations:update-status --dry-run] in background  114.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:45:08 Running ['artisan' recommendations:send-reminders --dry-run] in background  11.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:45:08 Running ['artisan' emails:retry-failed --dry-run] in background  17.35ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:45:08 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:45:08 Running ['artisan' system:sync-recruitment-periods] in background  23.71ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:50:10 Running ['artisan' recommendations:update-status --dry-run] in background  97.84ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:50:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.19ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:50:11 Running ['artisan' emails:retry-failed --dry-run] in background  16.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:50:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:50:11 Running ['artisan' system:sync-recruitment-periods] in background  19.68ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 15:55:10 Running ['artisan' recommendations:update-status --dry-run] in background  103.33ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:55:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  23.87ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:55:11 Running ['artisan' emails:retry-failed --dry-run] in background  22.85ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:55:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  30.63ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 15:55:11 Running ['artisan' system:sync-recruitment-periods] in background  21.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:00:09 Running ['artisan' recommendations:update-status --dry-run] in background  95.51ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:00:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  23.57ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:00:09 Running ['artisan' emails:retry-failed --dry-run] in background  17.72ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:00:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.08ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:00:09 Running ['artisan' system:sync-recruitment-periods] in background  15.09ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:05:10 Running ['artisan' recommendations:update-status --dry-run] in background  100.22ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:05:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.28ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:05:10 Running ['artisan' emails:retry-failed --dry-run] in background  13.64ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:05:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  23.30ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:05:11 Running ['artisan' system:sync-recruitment-periods] in background  25.97ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:10:15 Running ['artisan' recommendations:update-status --dry-run] in background  112.40ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:10:15 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.39ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:10:15 Running ['artisan' emails:retry-failed --dry-run] in background  14.01ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:10:15 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  27.93ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:10:15 Running ['artisan' system:sync-recruitment-periods] in background  31.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:15:10 Running ['artisan' recommendations:update-status --dry-run] in background  93.78ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:15:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  13.00ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:15:10 Running ['artisan' emails:retry-failed --dry-run] in background  17.27ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:15:10 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  21.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:15:10 Running ['artisan' system:sync-recruitment-periods] in background  22.76ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:20:08 Running ['artisan' recommendations:update-status --dry-run] in background  112.81ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:20:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  12.79ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:20:09 Running ['artisan' emails:retry-failed --dry-run] in background  14.36ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:20:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.03ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:20:09 Running ['artisan' system:sync-recruitment-periods] in background  14.73ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:25:10 Running ['artisan' recommendations:update-status --dry-run] in background  105.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:25:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  14.37ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:25:10 Running ['artisan' emails:retry-failed --dry-run] in background  13.93ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:25:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  18.66ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:25:11 Running ['artisan' system:sync-recruitment-periods] in background  20.07ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:30:30 Running ['artisan' recommendations:update-status --dry-run] in background  512.00ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:30:31 Running ['artisan' recommendations:send-reminders --dry-run] in background  133.13ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:30:32 Running ['artisan' emails:retry-failed --dry-run] in background  88.16ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:30:32 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  94.55ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:30:32 Running ['artisan' system:sync-recruitment-periods] in background  85.42ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:35:10 Running ['artisan' recommendations:update-status --dry-run] in background  90.93ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:35:11 Running ['artisan' recommendations:send-reminders --dry-run] in background  27.19ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:35:11 Running ['artisan' emails:retry-failed --dry-run] in background  24.69ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:35:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  22.15ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:35:11 Running ['artisan' system:sync-recruitment-periods] in background  16.89ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:40:10 Running ['artisan' recommendations:update-status --dry-run] in background  82.23ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:40:10 Running ['artisan' recommendations:send-reminders --dry-run] in background  20.26ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:40:10 Running ['artisan' emails:retry-failed --dry-run] in background  16.31ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:40:11 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  20.11ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:40:11 Running ['artisan' system:sync-recruitment-periods] in background  19.10ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:45:10 Running ['artisan' recommendations:update-status --dry-run] in background  137.04ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:45:09 Running ['artisan' recommendations:send-reminders --dry-run] in background  30.32ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:45:09 Running ['artisan' emails:retry-failed --dry-run] in background  21.83ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:45:09 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  15.77ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:45:09 Running ['artisan' system:sync-recruitment-periods] in background  17.41ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  


  2025-08-07 16:50:12 Running ['artisan' recommendations:update-status --dry-run] in background  98.22ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:update-status --dry-run >> 'storage/logs/recommendation.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-645c92e385b2cd8a1bd5a335b80eeffb037de427" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:50:13 Running ['artisan' recommendations:send-reminders --dry-run] in background  21.38ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' recommendations:send-reminders --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f9aaf7791cd2ffe614c65d19bb23acb42e0e81db" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:50:13 Running ['artisan' emails:retry-failed --dry-run] in background  35.19ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' emails:retry-failed --dry-run >> 'storage/logs/email.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-f24681308472a309d24dbbb9dbf211f989950052" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:50:13 Running ['artisan' pdf:cleanup --days=7 --dry-run] in background  39.48ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' pdf:cleanup --days=7 --dry-run >> 'storage/logs/pdf-cleanup.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-0aa0e00d41d5b984745db0ece25233cf253c4b64" "$?") > '/dev/null' 2>&1 &  
  2025-08-07 16:50:13 Running ['artisan' system:sync-recruitment-periods] in background  35.18ms DONE
  ⇂ ('/usr/bin/php8.3' 'artisan' system:sync-recruitment-periods >> 'storage/logs/recruitment-sync.log' 2>&1 ; '/usr/bin/php8.3' 'artisan' schedule:finish "framework/schedule-2c65be3b8e1abf73a19d3b65e85734306c647837" "$?") > '/dev/null' 2>&1 &  


   INFO  No scheduled commands are ready to run.  


   INFO  No scheduled commands are ready to run.  

