<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\LoginLog;
use App\Services\AuthenticationService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

/**
 * 管理員認證控制器
 *
 * 處理管理員的傳統帳密登入
 * 使用統一的 AuthenticationService 進行認證處理
 */
class AuthenticatedSessionController extends Controller
{
    protected AuthenticationService $authService;

    public function __construct(AuthenticationService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * 顯示管理員登入頁面
     */
    public function index(Request $request): Response
    {
        return Inertia::render('auth/Login', [
            'status' => $request->session()->get('status'),
        ]);
    }

    /**
     * 處理管理員登入請求
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        try {
            $request->authenticate();
            $request->session()->regenerate();

            // 記錄登入成功
            LoginLog::logSuccess(Auth::id());

            return redirect()->intended(route('dashboard', absolute: false));
        } catch (\Exception $e) {
            // 記錄登入失敗
            LoginLog::logFailure(null, $e->getMessage());
            throw $e;
        }
    }

    /**
     * 處理管理員登出
     * 使用統一認證服務處理登出邏輯
     */
    public function destroy(): RedirectResponse
    {
        $this->authService->logout();
        return redirect('/');
    }
}
