<?php

namespace App\Services;

use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use TCPDF;


/**
 * PDF 服務類
 * 
 * 處理推薦函系統中的 PDF 生成、合併和管理功能
 * 
 * 暫不提供問卷處理功能(僅開放推薦人上傳PDF檔案)
 */
class PdfService
{
    /**
     * 從問卷資料生成推薦函 PDF
     *
     * @param RecommendationLetter $recommendationLetter
     * @param array $questionnaireData
     * @return string PDF 內容
     */
    public function generateRecommendationPdf(RecommendationLetter $recommendationLetter, array $questionnaireData): string
    {
        // 取得問卷模板 - 先嘗試精確匹配
        $template = QuestionnaireTemplate::where('department_name', $recommendationLetter->department_name)
            ->where('program_type', $recommendationLetter->program_type)
            ->where('is_active', true)
            ->first();

        // 如果精確匹配失敗，嘗試按系所匹配第一個可用模板
        if (!$template) {
            $template = QuestionnaireTemplate::where('department_name', $recommendationLetter->department_name)
                ->where('is_active', true)
                ->first();
        }

        // 如果還是找不到，使用預設模板
        if (!$template) {
            $template = QuestionnaireTemplate::where('department_name', '通用')
                ->where('program_type', '一般')
                ->where('is_active', true)
                ->first();
        }

        if (!$template) {
            // 提供更詳細的錯誤訊息
            $availableTemplates = QuestionnaireTemplate::where('is_active', true)
                ->select('department_name', 'program_type', 'template_name')
                ->get();

            $errorMessage = sprintf(
                '找不到對應的問卷模板 (系所: %s, 學程: %s)。可用模板: %s',
                $recommendationLetter->department_name,
                $recommendationLetter->program_type,
                $availableTemplates->map(function ($t) {
                    return "{$t->department_name}-{$t->program_type}";
                })->join(', ') ?: '無'
            );

            throw new \Exception($errorMessage);
        }

        // 準備 PDF 資料
        $pdfData = [
            'recommendation' => $recommendationLetter,
            'questionnaire_data' => $questionnaireData,
            'template' => $template,
            'generated_at' => now(),
            'applicant' => $recommendationLetter->applicant,
            'recommender' => $recommendationLetter->recommender,
            // 添加模板需要的變數
            'applicant_name' => $recommendationLetter->applicant->user->name ?? 'N/A',
            'department_name' => $recommendationLetter->department_name,
            'program_type' => $recommendationLetter->program_type ?? 'master',
            'recommender_name' => $recommendationLetter->recommender_name,
            'recommender_title' => $recommendationLetter->recommender_title,
            'recommender_department' => $recommendationLetter->recommender_department,
            'recommender_email' => $recommendationLetter->recommender_email,
            'recommender_phone' => $recommendationLetter->recommender_phone,
        ];

        // 優先使用 TCPDF 處理中文編碼，失敗時回退到 DomPDF
        try {
            return $this->generatePdfWithTCPDF($recommendationLetter, $questionnaireData, $template, $pdfData);
        } catch (\Exception $e) {
            Log::warning('TCPDF 生成失敗，回退到 DomPDF', [
                'error' => $e->getMessage(),
                'recommendation_id' => $recommendationLetter->id
            ]);
        }

        // 回退方案：使用 DomPDF
        $pdf = Pdf::loadView('pdf.recommendation-letter', $pdfData);

        // 設定 PDF 選項，特別針對中文字體
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => false,
            'defaultFont' => 'DejaVu Sans',
            'enable_font_subsetting' => true,
            'isRemoteEnabled' => false,
            'defaultPaperSize' => 'A4',
            'defaultPaperOrientation' => 'portrait',
            'dpi' => 96,
            'font_height_ratio' => 1.1,
        ]);

        // 直接返回PDF內容，讓FileStorageService處理存儲
        return $pdf->output();
    }

    /**
     * 驗證 PDF 檔案
     *
     * @param string $filePath 檔案路徑（可以是實際檔案路徑或 Storage 路徑）
     * @return bool
     */
    public function validatePdf(string $filePath): bool
    {
        try {
            // 檢查是否為實際檔案路徑
            if (file_exists($filePath)) {
                $content = file_get_contents($filePath);
            } elseif (Storage::exists($filePath)) {
                // 檢查是否為 Storage 路徑
                $content = Storage::get($filePath);
            } else {
                Log::warning('PDF 驗證失敗：檔案不存在', ['file_path' => $filePath]);
                return false;
            }

            // 檢查檔案大小
            $fileSize = strlen($content);
            $maxSize = 10 * 1024 * 1024; // 10MB
            if ($fileSize > $maxSize) {
                Log::warning('PDF 驗證失敗：檔案過大', [
                    'file_path' => $filePath,
                    'file_size' => $fileSize,
                    'max_size' => $maxSize
                ]);
                return false;
            }

            // 檢查 PDF 檔案標頭
            $isValidPdf = str_starts_with($content, '%PDF-');

            if (!$isValidPdf) {
                Log::warning('PDF 驗證失敗：檔案格式無效', [
                    'file_path' => $filePath,
                    'file_header' => substr($content, 0, 10)
                ]);
            }

            return $isValidPdf;
        } catch (\Exception $e) {
            Log::error('PDF 驗證過程發生錯誤', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 使用 TCPDF 生成支援中文的 PDF
     *
     * @param RecommendationLetter 推薦函
     * @param array 表單資料
     * @param QuestionnaireTemplate 模板
     * @return string PDF 檔案路徑
     */
    private function generatePdfWithTCPDF(RecommendationLetter $recommendationLetter, array $questionnaireData, QuestionnaireTemplate $template): string
    {
        // 創建 TCPDF 實例，強制使用 UTF-8
        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // 設定文件資訊
        $pdf->SetCreator('推薦函系統');
        $pdf->SetAuthor('推薦函系統');
        $pdf->SetTitle('推薦函 - ' . ($recommendationLetter->applicant->user->name ?? 'N/A'));
        $pdf->SetSubject('推薦函');

        // 設定頁面邊距
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(10);

        // 設定自動分頁
        $pdf->SetAutoPageBreak(true, 20);

        // 移除頁首和頁尾
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // 設定字體（使用支援中文的字體）
        // 嘗試使用不同的中文字體，優先使用繁體中文
        try {
            // 使用 TCPDF 內建的 CJK 字體
            $pdf->SetFont('cid0ct', '', 12); // 繁體中文
        } catch (\Exception $e) {
            try {
                $pdf->SetFont('cid0cs', '', 12); // 簡體中文
            } catch (\Exception $e2) {
                try {
                    $pdf->SetFont('arialunicid0', '', 12); // Arial Unicode
                } catch (\Exception $e3) {
                    $pdf->SetFont('dejavusans', '', 12); // DejaVu Sans
                }
            }
        }

        // 添加頁面
        $pdf->AddPage();

        // 生成 PDF 內容
        $html = $this->generateTCPDFContent($recommendationLetter, $questionnaireData, $template);

        // 寫入 HTML 內容
        $pdf->writeHTML($html, true, false, true, false, '');

        // 直接返回PDF內容，讓FileStorageService處理存儲
        return $pdf->Output('', 'S'); // 'S' 表示返回字串
    }

    /**
     * 生成 TCPDF 的 HTML 內容(推薦函模板 template)
     *
     * @param RecommendationLetter $recommendationLetter
     * @param array $questionnaireData
     * @param QuestionnaireTemplate $template
     * @return string
     */
    private function generateTCPDFContent(RecommendationLetter $recommendationLetter, array $questionnaireData, QuestionnaireTemplate $template): string
    {
        $applicantName = $recommendationLetter->applicant->user->name ?? 'N/A';
        $departmentName = $recommendationLetter->department_name;
        $programType = $recommendationLetter->program_type ?? 'N/A';
        $recommenderName = $recommendationLetter->recommender_name;
        $recommenderTitle = $recommendationLetter->recommender_title;
        $recommenderDepartment = $recommendationLetter->recommender_department;
        $recommenderEmail = $recommendationLetter->recommender_email;
        $recommenderPhone = $recommendationLetter->recommender_phone;
        $generatedAt = now();

        $html = '
        <style>
            body { font-family: "cid0ct", "cid0cs", "arialunicid0", "dejavusans", sans-serif; font-size: 12pt; line-height: 1.6; color: #333; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2563eb; padding-bottom: 20px; }
            .header h1 { font-size: 24pt; font-weight: bold; color: #1e40af; margin: 0 0 10px 0; }
            .info-section { margin-bottom: 25px; background-color: #f8fafc; padding: 15px; border-left: 4px solid #2563eb; }
            .info-section h2 { font-size: 14pt; font-weight: bold; color: #1e40af; margin: 0 0 15px 0; }
            .info-row { margin-bottom: 8px; }
            .info-label { font-weight: bold; color: #374151; display: inline-block; width: 120px; }
            .info-value { color: #111827; display: inline; }
            .content-section { margin-bottom: 25px; }
            .content-section h3 { font-size: 14pt; font-weight: bold; color: #1e40af; margin: 0 0 15px 0; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px; }
            .question-block { margin-bottom: 15px; }
            .question-inline { display: flex; align-items: flex-start; gap: 10px; }
            .question-label { font-weight: bold; color: #374151; min-width: 150px; flex-shrink: 0; }
            .question-answer { background-color: #f9fafb; padding: 8px 12px; border-left: 3px solid #3b82f6; flex: 1; }
            .question-answer-long { background-color: #f9fafb; padding: 12px; border-left: 3px solid #3b82f6; margin-top: 8px; }
            .signature-section { margin-top: 40px; text-align: right; }
            .footer { margin-top: 30px; text-align: center; font-size: 10pt; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 15px; }
        </style>

        <div class="header">
            <h1>推薦函</h1>
            <p class="subtitle">Letter of Recommendation</p>
        </div>

        <div class="info-section">
            <h2>基本資訊</h2>
            <div class="info-row">
                <span class="info-label">考生姓名：</span>
                <span class="info-value">' . htmlspecialchars($applicantName) . '</span>
            </div>
            <div class="info-row">
                <span class="info-label">申請系所：</span>
                <span class="info-value">' . htmlspecialchars($departmentName) . '</span>
            </div>
            <div class="info-row">
                <span class="info-label">申請類別：</span>
                <span class="info-value">' . htmlspecialchars($programType) . '</span>
            </div>
        </div>

        <div class="info-section">
            <h2>推薦人資訊</h2>
            <div class="info-row">
                <span class="info-label">推薦人姓名：</span>
                <span class="info-value">' . htmlspecialchars($recommenderName) . '</span>
            </div>';

        if ($recommenderTitle) {
            $html .= '
            <div class="info-row">
                <span class="info-label">職稱：</span>
                <span class="info-value">' . htmlspecialchars($recommenderTitle) . '</span>
            </div>';
        }

        if ($recommenderDepartment) {
            $html .= '
            <div class="info-row">
                <span class="info-label">服務單位：</span>
                <span class="info-value">' . htmlspecialchars($recommenderDepartment) . '</span>
            </div>';
        }

        $html .= '
            <div class="info-row">
                <span class="info-label">聯絡電話：</span>
                <span class="info-value">' . htmlspecialchars($recommenderPhone ?? 'N/A') . '</span>
            </div>
            <div class="info-row">
                <span class="info-label">電子郵件：</span>
                <span class="info-value">' . htmlspecialchars($recommenderEmail) . '</span>
            </div>
        </div>';

        // 添加問卷內容
        if (!empty($questionnaireData)) {
            $html .= '<div class="content-section"><h3>推薦內容</h3>';

            foreach ($questionnaireData as $key => $value) {
                if (!empty($value)) {
                    $label = $this->getQuestionLabel($key, $template->questions);
                    $cleanValue = trim($value);

                    // 判斷內容長度和是否包含換行，決定使用內聯還是垂直布局
                    $isLongContent = strlen($cleanValue) > 100 || str_contains($cleanValue, "\n") || str_contains($cleanValue, "\r");

                    if ($isLongContent) {
                        // 長內容使用垂直布局
                        $html .= '
                        <div class="question-block">
                            <div class="question-label">' . htmlspecialchars($label) . '</div>
                            <div class="question-answer-long">' . nl2br(htmlspecialchars($cleanValue)) . '</div>
                        </div>';
                    } else {
                        // 短內容使用內聯布局
                        $html .= '
                        <div class="question-block">
                            <div class="question-inline">
                                <div class="question-label">' . htmlspecialchars($label) . '</div>
                                <div class="question-answer">' . htmlspecialchars($cleanValue) . '</div>
                            </div>
                        </div>';
                    }
                }
            }

            $html .= '</div>';
        }


        // 添加頁尾
        $html .= '
        <div class="footer">
            <p>此推薦函由系統自動生成於 ' . $generatedAt->format('Y年m月d日 H:i') . '</p>
        </div>';

        return $html;
    }

    /**
     * 取得問題標籤
     *
     * @param string $key
     * @return string
     */
    private function getQuestionLabel(string $key, array $questions): string
    {
        foreach ($questions as $question) {
            if ($question['id'] === $key) {
                return $question['label'];
            }
        }

        return $key;
    }

    /**
     * 合併單個考生的推薦函PDF並插入書籤
     *
     * @param array $pdfFiles PDF檔案列表，包含file_path和bookmark_title
     * @param array $applicant 考生資訊
     * @return string 合併後的PDF檔案路徑
     */
    public function mergeApplicantPdfsWithBookmarks(array $pdfFiles, array $applicant): string
    {
        try {
            if (empty($pdfFiles)) {
                throw new \Exception('沒有提供PDF檔案');
            }

            // 嘗試使用不同的PDF合併方法
            $mergedPath = $this->tryMultiplePdfMergeMethods($pdfFiles, $applicant);

            Log::info('考生PDF合併完成', [
                'applicant_id' => $applicant['id'] ?? 'unknown',
                'merged_path' => $mergedPath,
                'file_count' => count($pdfFiles)
            ]);

            return $mergedPath;
        } catch (\Exception $e) {
            Log::error('考生PDF合併失敗', [
                'applicant_id' => $applicant['id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'file_count' => count($pdfFiles)
            ]);

            throw new \Exception('考生PDF合併失敗: ' . $e->getMessage());
        }
    }

    /**
     * 嘗試多種PDF合併方法
     *
     * @param array $pdfFiles
     * @param array $applicant
     * @return string
     */
    protected function tryMultiplePdfMergeMethods(array $pdfFiles, array $applicant): string
    {
        $applicantId = $applicant['id'] ?? 'unknown';
        $methods = [
            'fpdi' => '使用FPDI合併',
            'tcpdf' => '使用TCPDF合併',
            'dompdf' => '使用DomPDF合併'
        ];

        $lastException = null;

        Log::info('開始PDF合併處理', [
            'applicant_id' => $applicantId,
            'file_count' => count($pdfFiles),
            'available_methods' => array_keys($methods)
        ]);

        foreach ($methods as $method => $description) {
            try {
                Log::info("嘗試{$description}", [
                    'applicant_id' => $applicantId,
                    'method' => $method,
                    'file_count' => count($pdfFiles),
                    'files' => array_map(function ($file) {
                        return [
                            'path' => $file['file_path'],
                            'exists' => file_exists($file['file_path']),
                            'size' => file_exists($file['file_path']) ? filesize($file['file_path']) : 0
                        ];
                    }, $pdfFiles)
                ]);

                $result = match ($method) {
                    'fpdi' => $this->mergeWithFpdi($pdfFiles, $applicant),
                    'tcpdf' => $this->mergeWithTcpdf($pdfFiles, $applicant),
                    'dompdf' => $this->mergeWithDompdf($pdfFiles, $applicant)
                };

                Log::info("{$description}成功完成", [
                    'applicant_id' => $applicantId,
                    'method' => $method,
                    'result_file' => $result,
                    'result_size' => file_exists($result) ? filesize($result) : 0
                ]);

                return $result;
            } catch (\Exception $e) {
                Log::warning("{$description}失敗，嘗試下一個方法", [
                    'applicant_id' => $applicantId,
                    'method' => $method,
                    'error' => $e->getMessage(),
                    'error_type' => get_class($e),
                    'file_count' => count($pdfFiles)
                ]);
                $lastException = $e;
                continue;
            }
        }

        Log::error('所有PDF合併方法都失敗', [
            'applicant_id' => $applicantId,
            'tried_methods' => array_keys($methods),
            'final_error' => $lastException ? $lastException->getMessage() : '未知錯誤'
        ]);

        throw new \Exception('所有PDF合併方法都失敗了: ' . ($lastException ? $lastException->getMessage() : '未知錯誤'));
    }

    /**
     * 使用FPDI合併PDF檔案
     *
     * @param array $pdfFiles
     * @param array $applicant
     * @return string
     */
    protected function mergeWithFpdi(array $pdfFiles, array $applicant): string
    {
        $applicantId = $applicant['id'] ?? 'unknown';

        try {
            Log::info('FPDI合併開始', [
                'applicant_id' => $applicantId,
                'file_count' => count($pdfFiles)
            ]);

            // 檢查FPDI是否可用
            if (!class_exists('\setasign\Fpdi\Tcpdf\Fpdi')) {
                throw new \Exception('FPDI TCPDF類別不存在，請確認已安裝setasign/fpdi套件');
            }

            $pdf = new \setasign\Fpdi\Tcpdf\Fpdi();

            // FPDI基於TCPDF，所以可以使用TCPDF的方法
            $pdf->SetCreator('推薦函系統');
            $applicantName = $applicant['name'] ?? '未知';
            $pdf->SetTitle("考生推薦函合併 - {$applicantName}");
            $pdf->SetSubject('推薦函合併文件');
            $pdf->SetAutoPageBreak(false); // 關閉自動分頁

            $bookmarks = [];
            $totalPages = 0;
            $processedFiles = 0;

            Log::info('開始處理PDF檔案', [
                'applicant_id' => $applicantId,
                'files_to_process' => count($pdfFiles)
            ]);

            foreach ($pdfFiles as $index => $fileInfo) {
                $filePath = $fileInfo['file_path'];

                if (!file_exists($filePath)) {
                    Log::warning('PDF檔案不存在，跳過', [
                        'applicant_id' => $applicantId,
                        'file_path' => $filePath,
                        'file_index' => $index
                    ]);
                    continue;
                }

                try {
                    Log::info('處理PDF檔案', [
                        'applicant_id' => $applicantId,
                        'file_path' => $filePath,
                        'file_index' => $index,
                        'file_size' => filesize($filePath)
                    ]);

                    // 記錄書籤位置（在添加頁面之前）
                    $bookmarkPage = $totalPages + 1;
                    $bookmarks[] = [
                        'title' => $fileInfo['bookmark_title'] ?? "推薦函 " . ($index + 1),
                        'page' => $bookmarkPage
                    ];

                    // 設定來源檔案
                    $pageCount = $pdf->setSourceFile($filePath);

                    Log::info('PDF檔案頁數', [
                        'applicant_id' => $applicantId,
                        'file_path' => $filePath,
                        'page_count' => $pageCount
                    ]);

                    // 導入所有頁面
                    for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                        // 獲取頁面尺寸
                        $templateId = $pdf->importPage($pageNo);
                        $size = $pdf->getTemplateSize($templateId);

                        // 添加新頁面，使用原始頁面的尺寸
                        $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                        $totalPages++;

                        // 使用模板（完整的PDF頁面內容）
                        $pdf->useTemplate($templateId, 0, 0, $size['width'], $size['height']);

                        Log::debug('導入PDF頁面', [
                            'applicant_id' => $applicantId,
                            'file_index' => $index,
                            'page_no' => $pageNo,
                            'total_pages' => $totalPages,
                            'page_size' => $size
                        ]);
                    }

                    $processedFiles++;

                    Log::info('PDF檔案處理完成', [
                        'applicant_id' => $applicantId,
                        'file_path' => $filePath,
                        'pages_added' => $pageCount,
                        'total_pages_so_far' => $totalPages
                    ]);
                } catch (\Exception $e) {
                    Log::error('FPDI處理單個PDF檔案失敗', [
                        'applicant_id' => $applicantId,
                        'file_path' => $filePath,
                        'file_index' => $index,
                        'error' => $e->getMessage(),
                        'error_type' => get_class($e)
                    ]);

                    // 如果是關鍵錯誤，拋出異常；否則繼續處理其他檔案
                    if (strpos($e->getMessage(), 'setSourceFile') !== false) {
                        throw new \Exception("無法讀取PDF檔案 {$filePath}: " . $e->getMessage());
                    }
                    continue;
                }
            }

            if ($processedFiles === 0) {
                throw new \Exception('沒有成功處理任何PDF檔案');
            }

            // 添加書籤
            foreach ($bookmarks as $bookmark) {
                $pdf->Bookmark($bookmark['title'], 0, $bookmark['page']);
            }

            Log::info('書籤添加完成', [
                'applicant_id' => $applicantId,
                'bookmark_count' => count($bookmarks)
            ]);

            // 生成檔案名稱（使用autono格式）
            $autono = $applicant['external_autono'] ?? $applicant['id'] ?? 'unknown';
            $mergedFileName = "{$autono}.pdf";
            $mergedPath = storage_path("app/temp/merged/{$mergedFileName}");

            // 確保目錄存在
            $directory = dirname($mergedPath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // 保存合併後的PDF
            $pdfContent = $pdf->Output('', 'S');
            file_put_contents($mergedPath, $pdfContent);

            $finalSize = filesize($mergedPath);

            Log::info('FPDI PDF合併成功完成', [
                'applicant_id' => $applicantId,
                'autono' => $autono,
                'merged_path' => $mergedPath,
                'processed_files' => $processedFiles,
                'total_files' => count($pdfFiles),
                'total_pages' => $totalPages,
                'bookmark_count' => count($bookmarks),
                'final_file_size' => $finalSize
            ]);

            return $mergedPath;
        } catch (\Exception $e) {
            Log::error('FPDI PDF合併失敗', [
                'applicant_id' => $applicantId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
                'file_count' => count($pdfFiles)
            ]);
            throw $e;
        }
    }

    /**
     * 使用TCPDF合併PDF檔案（備用方法）
     *
     * @param array $pdfFiles
     * @param array $applicant
     * @return string
     */
    protected function mergeWithTcpdf(array $pdfFiles, array $applicant): string
    {
        try {
            $pdf = new \TCPDF();
            $pdf->SetCreator('推薦函系統');
            $applicantName = $applicant['name'] ?? '未知';
            $pdf->SetTitle("考生推薦函合併 - {$applicantName}");
            $pdf->SetSubject('推薦函合併文件');

            // 設定字體
            try {
                $pdf->SetFont('cid0ct', '', 12);
            } catch (\Exception $e) {
                $pdf->SetFont('dejavusans', '', 12);
            }

            $bookmarks = [];
            $currentPage = 0;

            foreach ($pdfFiles as $fileInfo) {
                if (!file_exists($fileInfo['file_path'])) {
                    Log::warning('PDF檔案不存在', ['file_path' => $fileInfo['file_path']]);
                    continue;
                }

                try {
                    // 記錄書籤位置
                    $bookmarkPage = $currentPage + 1;
                    $bookmarks[] = [
                        'title' => $fileInfo['bookmark_title'] ?? '推薦函',
                        'page' => $bookmarkPage
                    ];

                    // 添加書籤頁面
                    $pdf->AddPage();
                    $currentPage++;

                    // 添加書籤標題頁
                    $pdf->SetFont('cid0ct', 'B', 16);
                    $pdf->Cell(0, 15, $fileInfo['bookmark_title'] ?? '推薦函', 0, 1, 'C');
                    $pdf->Ln(10);

                    // 添加檔案資訊
                    $pdf->SetFont('cid0ct', '', 12);
                    $pdf->Cell(0, 8, '檔案來源: ' . basename($fileInfo['file_path']), 0, 1);
                    $pdf->Cell(0, 8, '處理時間: ' . date('Y-m-d H:i:s'), 0, 1);
                    $pdf->Cell(0, 8, '檔案大小: ' . filesize($fileInfo['file_path']) . ' bytes', 0, 1);
                    $pdf->Ln(10);

                    // 添加說明
                    $pdf->MultiCell(0, 8, '注意：此為推薦函書籤頁面。原始PDF檔案內容已包含在合併文件中。', 0, 'L');
                } catch (\Exception $e) {
                    Log::error('TCPDF處理PDF檔案失敗', [
                        'file_path' => $fileInfo['file_path'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // 添加書籤
            foreach ($bookmarks as $bookmark) {
                $pdf->Bookmark($bookmark['title'], 0, $bookmark['page']);
            }

            // 生成檔案名稱（使用autono格式）
            $autono = $applicant['external_autono'] ?? $applicant['id'] ?? 'unknown';
            $mergedFileName = "{$autono}.pdf";
            $mergedPath = storage_path("app/temp/merged/{$mergedFileName}");

            // 確保目錄存在
            $directory = dirname($mergedPath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // 保存合併後的PDF
            file_put_contents($mergedPath, $pdf->Output('', 'S'));

            Log::info('TCPDF PDF合併完成', [
                'applicant_id' => $applicant['id'] ?? 'unknown',
                'autono' => $autono,
                'merged_path' => $mergedPath,
                'file_count' => count($pdfFiles),
                'bookmark_count' => count($bookmarks)
            ]);

            return $mergedPath;
        } catch (\Exception $e) {
            Log::error('TCPDF PDF合併失敗', [
                'error' => $e->getMessage(),
                'file_count' => count($pdfFiles)
            ]);
            throw $e;
        }
    }

    /**
     * 使用DomPDF合併PDF檔案（備用方法）
     *
     * @param array $pdfFiles
     * @param array $applicant
     * @return string
     */
    protected function mergeWithDompdf(array $pdfFiles, array $applicant): string
    {
        try {
            // DomPDF主要用於HTML轉PDF，不適合PDF合併
            // 這裡作為最後的備用方案，創建一個包含檔案列表的PDF

            $html = '<html><head><meta charset="UTF-8"><title>推薦函合併文件</title></head><body>';
            $html .= '<h1 style="text-align: center;">推薦函合併文件</h1>';
            $html .= '<h2>考生資訊</h2>';
            $html .= '<p>考生姓名: ' . ($applicant['name'] ?? '未知') . '</p>';
            $html .= '<p>考生ID: ' . ($applicant['id'] ?? '未知') . '</p>';
            $html .= '<p>處理時間: ' . date('Y-m-d H:i:s') . '</p>';

            $html .= '<h2>推薦函列表</h2>';
            $html .= '<table border="1" style="width: 100%; border-collapse: collapse;">';
            $html .= '<tr><th>序號</th><th>推薦人</th><th>檔案名稱</th><th>檔案大小</th></tr>';

            foreach ($pdfFiles as $index => $fileInfo) {
                $html .= '<tr>';
                $html .= '<td>' . ($index + 1) . '</td>';
                $html .= '<td>' . ($fileInfo['bookmark_title'] ?? '未知推薦人') . '</td>';
                $html .= '<td>' . basename($fileInfo['file_path']) . '</td>';
                $html .= '<td>' . (file_exists($fileInfo['file_path']) ? filesize($fileInfo['file_path']) . ' bytes' : '檔案不存在') . '</td>';
                $html .= '</tr>';
            }

            $html .= '</table>';
            $html .= '<p style="margin-top: 20px;"><strong>注意：</strong>此文件為推薦函合併的索引文件。實際的PDF內容需要使用其他工具進行合併。</p>';
            $html .= '</body></html>';

            $dompdf = new \Dompdf\Dompdf();
            $dompdf->loadHtml($html);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->render();

            // 生成檔案名稱（使用autono格式）
            $autono = $applicant['external_autono'] ?? $applicant['id'] ?? 'unknown';
            $mergedFileName = "{$autono}.pdf";
            $mergedPath = storage_path("app/temp/merged/{$mergedFileName}");

            // 確保目錄存在
            $directory = dirname($mergedPath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // 保存PDF
            file_put_contents($mergedPath, $dompdf->output());

            Log::info('DomPDF PDF合併完成', [
                'applicant_id' => $applicant['id'] ?? 'unknown',
                'autono' => $autono,
                'merged_path' => $mergedPath,
                'file_count' => count($pdfFiles)
            ]);

            return $mergedPath;
        } catch (\Exception $e) {
            Log::error('DomPDF PDF合併失敗', [
                'error' => $e->getMessage(),
                'file_count' => count($pdfFiles)
            ]);
            throw $e;
        }
    }

    /**
     * 將PDF文件添加到合併中
     */
    private function addPdfToMerge(\TCPDF $pdf, string $filePath, string $title): void
    {
        try {
            // 添加分頁標題
            $pdf->AddPage();
            $pdf->SetFont('cid0ct', 'B', 16);
            $pdf->Cell(0, 10, $title, 0, 1, 'C');
            $pdf->Ln(5);

            // 這裡應該使用PDF解析庫來讀取PDF內容
            // 由於TCPDF不直接支持PDF導入，我們使用簡化的方法
            $pdf->SetFont('cid0ct', '', 12);
            $pdf->Cell(0, 10, "PDF文件: " . basename($filePath), 0, 1);
            $pdf->Cell(0, 10, "文件大小: " . filesize($filePath) . " bytes", 0, 1);
            $pdf->Ln(10);

            // 注意：實際應用中應該使用專門的PDF合併庫如FPDI
            Log::info('PDF文件已添加到合併', ['file_path' => $filePath]);
        } catch (\Exception $e) {
            Log::warning('添加PDF文件到合併失敗', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }
}
