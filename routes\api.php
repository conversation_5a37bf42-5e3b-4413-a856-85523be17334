<?php

use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Api\RecommendationApiController;
use App\Http\Controllers\Api\PdfMergeApiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 受保護的 API 路由群組
|--------------------------------------------------------------------------
|
| 注意：Laravel 會自動為 api.php 添加 /api 前綴
|
| 已實作API白名單中間件，僅允許來自授權系統(指定IP和User-Agent)的請求
| 因推薦函資料位於此系統，所以須開放給其他系統索取資料與狀態
*/

Route::middleware(['api.whitelist'])->group(function () {
    Route::get('/health', [RecommendationApiController::class, 'health'])->name('api.health'); // 提供API服務狀態
    Route::get('/info', [RecommendationApiController::class, 'info'])->name('api.info'); // 提供API版本和端點資訊

    /**
     * 推薦函相關API
     */
    Route::prefix('recommendations')->name('api.recommendations.')->group(function () {
        Route::get('/stats', [RecommendationApiController::class, 'getRecommendationStats'])->name('stats'); // 提供推薦函統計資料
    });

    /** 
     * PDF壓縮相關API
     */
    Route::prefix('pdf-merge')->name('api.pdf-merge.')->group(function () {
        Route::post('/start-merge', [PdfMergeApiController::class, 'startMerge'])->name('start'); // 啟動PDF壓縮任務
        Route::get('/merge-status', [PdfMergeApiController::class, 'getMergeStatus'])->name('status'); // 查詢壓縮任務狀態
    });

    /** 下載路由(提供合併端系統將壓縮完成的下載回去） */
    Route::get('/pdf-download/{taskId}', [PdfMergeApiController::class, 'download'])->name('api.pdf-merge.download')->where('taskId', '[a-zA-Z0-9_-]+');
});
