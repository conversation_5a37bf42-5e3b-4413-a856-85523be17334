<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * 建立推薦函系統相關資料表
     * 
     * 此遷移文件統一建立所有推薦函系統相關的資料表
     */
    public function up(): void
    {
        /**
         * 考生資料表
         * 儲存考生的基本資訊和第三方系統關聯
         */
        Schema::create('applicants', function (Blueprint $table) {
            $table->id()->comment('考生 ID');
            $table->foreignId('user_id')->comment('關聯到使用者表的 ID')->constrained()->cascadeOnDelete(); // 父表刪除時，關聯資料整筆刪除
            $table->string('exam_id')->comment('招生代碼 (對應第三方系統)');
            $table->integer('exam_year')->comment('招生年度(民國)');
            $table->string('external_uid')->comment('加密的考生 ID (須送回來源系統解密後查詢使用)'); // 資料來源為eapapi系統
            $table->string('phone')->comment('聯絡電話')->nullable();
            $table->timestamps();

            // 約束
            $table->unique(['exam_id', 'exam_year', 'user_id']); // 確保同一考生在同一年同一招生代碼下只能有一個考生記錄(不同exam_id以不同身分)
        });

        /**
         * 推薦人資料表
         * 儲存推薦人的詳細資訊和登入憑證
         */
        Schema::create('recommenders', function (Blueprint $table) {
            $table->id()->comment('推薦人 ID');
            $table->foreignId('user_id')->comment('關聯到使用者表的 ID')->constrained()->cascadeOnDelete(); // 父表刪除時，關聯資料整筆刪除
            $table->string('email')->comment('推薦人信箱')->index();
            $table->string('name')->comment('推薦人姓名');
            $table->string('title')->comment('稱謂/職稱')->nullable();
            $table->string('phone')->comment('聯絡電話')->nullable();
            $table->string('department')->comment('所屬部門/機構')->nullable()->index();

            $table->string('login_token')->comment('登入 Token')->unique();
            $table->string('exam_year')->comment('招生年度(民國)')->index();
            $table->string('exam_id')->comment('招生代碼(對應招生報名系統)')->index();
            $table->timestamp('last_login_at')->comment('最後登入時間')->nullable();
            $table->timestamp('token_expires_at')->comment('Token 過期時間')->nullable(); // 未使用
            $table->boolean('is_active')->comment('是否啟用')->default(true)->index(); // 用於推薦人有停用需求時
            $table->timestamps();

            // 約束
            $table->unique(['email', 'exam_year', 'exam_id']); // 同個信箱在同一年同一招生代碼下只能有一個推薦人

            // 索引
            $table->index(['department', 'exam_year']);
            $table->index(['is_active', 'exam_year']);
        });

        /**
         * 推薦函資料表
         * 儲存推薦函的詳細資訊、狀態和內容
         */
        Schema::create('recommendation_letters', function (Blueprint $table) {
            $table->id()->comment('推薦函 ID');

            // 關聯資訊
            $table->foreignId('applicant_id')->comment('考生 ID')->constrained()->cascadeOnDelete();
            $table->foreignId('recommender_id')->comment('推薦人 ID')->constrained()->cascadeOnDelete();

            $table->string('exam_id')->comment('招生代碼 (對應第三方系統)');
            $table->integer('exam_year')->comment('招生年度(民國)');
            $table->string('external_autono')->comment('考生報名流水號(流水號在同年度的招生代碼下唯一)')->index();

            // 推薦人資訊
            $table->string('recommender_email')->comment('推薦人信箱')->index();
            $table->string('recommender_name')->comment('推薦人姓名(快照)');
            $table->string('recommender_title')->comment('推薦人職稱(快照)')->nullable();
            $table->string('recommender_phone')->comment('推薦人電話(快照)')->nullable();
            $table->string('recommender_department')->comment('推薦人部門(快照)')->nullable();

            // 推薦函相關資訊
            $table->string('program_type')->comment('招生類別')->index();
            $table->string('department_name')->comment('報考類組(系所)')->index();
            $table->enum('status', ['pending',  'declined', 'submitted', 'withdrawn'])->comment('推薦函狀態(pending, declined, submitted, withdrawn)')->default('pending')->index();
            $table->integer('submit_count')->comment('提交次數')->default(0);
            $table->string('submission_type')->comment('提交方式(pdf, questionnaire)')->nullable();
            $table->string('pdf_path')->comment('PDF 檔案路徑')->nullable();
            $table->json('questionnaire_data')->comment('問卷資料 JSON')->nullable();

            // 時間戳記
            $table->timestamp('submitted_at')->comment('推薦人提交時間')->nullable();
            $table->timestamp('last_reminded_at')->comment('上次發送提醒時間')->nullable();
            $table->timestamps();

            // 約束
            $table->unique([
                'applicant_id',
                'recommender_email',
                'department_name',
                'program_type'
            ], 'unique_recommendation_per_group'); // 確保每個考生在同一群組只能邀請同一推薦人一次

            // 索引
            $table->index(['department_name', 'program_type']);
            $table->index(['applicant_id', 'status']);
            $table->index(['recommender_email', 'status']);
            $table->index(['status', 'created_at']);
        });

        /**
         * 問卷模板資料表
         * 儲存不同系所和學程的問卷模板
         */
        Schema::create('questionnaire_templates', function (Blueprint $table) {
            $table->id();
            $table->string('template_name')->comment('模板名稱');
            $table->text('description')->comment('模板描述')->nullable();
            $table->string('department_name')->comment('適用系所(提供給哪一個系所)')->index();
            $table->string('program_type')->comment('適用學程類型(對應...')->index();
            $table->json('questions')->comment('問題列表 JSON');
            $table->json('pdf_settings')->comment('PDF 生成設定 JSON')->nullable(); // 尚未使用，可擴充
            $table->boolean('is_active')->comment('是否啟用')->default(true)->index();
            $table->integer('sort_order')->comment('排序順序')->default(0);
            $table->timestamps();

            // 索引
            $table->index(['department_name', 'program_type', 'is_active'], 'q_template_dept_prog_active_idx');

            // 約束
            $table->unique(['template_name', 'department_name', 'program_type'], 'qtemp_name_dept_prog_unique'); // 確保在一個招生類別下同一系所只能有一個問卷模板
        });



        /**
         * 系統設定資料表
         * 儲存系統的各種設定參數
         */
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id()->comment('設定 ID');
            $table->string('key')->comment('設定鍵值')->unique();
            $table->text('value')->comment('設定值');
            $table->string('type')->comment('資料類型 (string, json, boolean, datetime...)')->default('string');
            $table->string('category')->comment('設定分類 (general, timing, security, email)')->default('general');
            $table->string('description')->comment('設定描述')->nullable();
            $table->boolean('is_active')->comment('是否啟用')->default(true);
            $table->timestamps();

            // 索引
            $table->index(['category', 'is_active']);
        });

        /**
         * 郵件記錄資料表
         * 記錄系統發送的所有郵件
         */
        Schema::create('email_logs', function (Blueprint $table) {
            $table->id()->comment('郵件日誌 ID');
            $table->foreignId('recommendation_letter_id')->comment('關聯到推薦函 ID')->nullable()->constrained()->cascadeOnDelete(); // 關聯推薦函
            $table->string('recipient_email')->index()->comment('收件人信箱');
            $table->string('recipient_name')->nullable()->comment('收件人姓名');
            /**
             * 郵件類型
             * invitation: 邀請信
             * reminder: 提醒信
             * notification: 通知信
             * ...
             */
            $table->string('email_type')->index()->comment('郵件類型(invitation, reminder, notification');
            $table->string('subject')->comment('郵件主旨');
            $table->text('content')->comment('郵件內容');
            $table->enum('status', ['pending', 'sent', 'failed', 'bounced'])->comment('發送狀態')->default('pending')->index();
            $table->unsignedInteger('retry_count')->comment('重試次數')->default(0);
            $table->text('error_message')->comment('錯誤訊息')->nullable();
            $table->timestamp('sent_at')->comment('發送時間')->nullable();
            $table->json('metadata')->comment('額外資料 JSON')->nullable();
            $table->timestamps();

            // 索引
            $table->index(['recipient_email', 'email_type']);
            $table->index(['status', 'created_at']);
            $table->index(['email_type', 'sent_at']);
        });

        /**
         * 登入紀錄（考生、推薦人、管理員）
         */
        Schema::create('login_logs', function (Blueprint $table) {
            $table->id()->comment('登入日誌 ID');
            $table->foreignId('user_id')->comment('關聯到使用者表的 ID')->nullable();
            $table->ipAddress('ip_address')->comment('IP地址')->nullable();
            $table->text('user_agent')->comment('用戶代理')->nullable();
            $table->boolean('success')->comment('登入是否成功')->default(true);
            $table->string('failure_reason')->comment('失敗原因')->nullable();
            $table->timestamp('login_at')->comment('登入時間')->nullable();
            $table->timestamps();
        });

        /**
         * 操作日誌資料表
         * 記錄詳細的系統操作日誌，用來記錄「誰做了什麼業務操作」
         */
        Schema::create('operation_logs', function (Blueprint $table) {
            $table->id()->comment('操作日誌 ID');
            $table->string('type')->comment('日誌類型 (operation, error, system)');
            $table->string('action')->comment('操作動作');
            $table->text('description')->comment('詳細描述');
            $table->foreignId('user_id')->comment('操作用戶ID')->nullable()->constrained()->nullOnDelete();
            $table->string('ip_address')->comment('IP地址')->nullable();
            $table->string('user_agent')->comment('用戶代理')->nullable();
            $table->json('metadata')->comment('額外資料')->nullable();
            $table->string('level')->comment('日誌級別 (debug, info, warning, error, critical)')->default('info');
            $table->timestamps();

            $table->index(['type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['level', 'created_at']);
        });

        /**
         * 系統 API 請求紀錄
         * 
         * 追蹤使用者訪問作業、API觸發狀態碼、請求參數等，用來記錄「系統技術層的行為是否成功」
         */
        Schema::create('system_logs', function (Blueprint $table) {
            $table->id()->comment('系統日誌 ID');
            $table->foreignId('user_id')->comment('關聯到使用者表的ID')->nullable();
            $table->string('action')->comment('API路由名稱');
            $table->json('request_payload')->comment('請求參數')->nullable();
            $table->json('response_payload')->comment('回應參數')->nullable();
            $table->integer('status_code')->comment('狀態碼')->nullable();
            $table->timestamps();
        });

        /**
         * PDF合併任務資料表
         * 追蹤PDF合併任務的狀態和進度
         */
        Schema::create('pdf_merge_tasks', function (Blueprint $table) {
            $table->id()->comment('PDF合併任務 ID');
            $table->string('task_id')->comment('任務唯一識別碼')->unique();
            $table->enum('status', ['processing', 'ready', 'failed', 'expired'])->comment('任務狀態')->default('processing');
            $table->integer('progress')->default(0)->comment('進度百分比 (0-100)');
            $table->json('parameters')->comment('合併參數')->nullable();
            $table->string('download_url')->comment('下載連結')->nullable();
            $table->string('zip_file_path')->comment('ZIP檔案路徑')->nullable();
            $table->integer('total_files')->comment('總檔案數')->default(0);
            $table->integer('processed_files')->comment('已處理檔案數')->default(0);
            $table->text('error_message')->comment('錯誤訊息')->nullable();
            $table->timestamp('expires_at')->comment('過期時間')->nullable();
            $table->timestamps();

            $table->index(['status', 'created_at']);
            $table->index('task_id');
            $table->index('expires_at');
        });

        /**
         * 使用者條款同意紀錄（可對應任意 user_type）
         */
        Schema::create('user_agreements', function (Blueprint $table) {
            $table->id()->comment('同意紀錄 ID');
            $table->morphs('user'); // 會產生 user_id + user_type，可關聯 Applicant 或 Recommender *Laravel 的多態關聯
            $table->boolean('agree_status')->comment('同意狀態');
            $table->timestamps();
        });
    }

    /**
     * 回滾遷移
     */
    public function down(): void
    {
        Schema::dropIfExists('user_agreements');
        Schema::dropIfExists('pdf_merge_tasks');
        Schema::dropIfExists('login_logs');
        Schema::dropIfExists('email_logs');
        Schema::dropIfExists('system_logs');
        Schema::dropIfExists('operation_logs');
        Schema::dropIfExists('system_settings');
        Schema::dropIfExists('questionnaire_templates');
        Schema::dropIfExists('recommendation_letters');
        Schema::dropIfExists('recommenders');
        Schema::dropIfExists('applicants');
    }
};
