import { Badge } from '@/components/ui/badge';

interface StatusBadgeProps {
    status: string;
    type?: 'recommendation' | 'system' | 'user' | 'generic' | 'log' | 'login';
    className?: string;
}

/**
 * 通用狀態徽章元件
 *
 * 根據不同類型和狀態顯示對應的徽章樣式
 *
 * @param {string} status - 狀態值
 * @param {string} [type='generic'] - 狀態類型，可選值有 'recommendation', 'system', 'user', 'generic'
 * @param {string} [className=''] - 附加的 CSS 類名
 * @returns {JSX.Element} 返回一個徽章元件
 */
export default function StatusBadge({ status, type = 'generic', className = '' }: StatusBadgeProps) {
    const getStatusConfig = (status: string, type: string) => {
        // 推薦函狀態
        if (type === 'recommendation') {
            switch (status) {
                case 'pending':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-yellow-100 text-yellow-800',
                        label: '待處理',
                    };
                case 'submitted':
                case 'completed':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-green-100 text-green-800',
                        label: '已提交',
                    };
                case 'declined':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-red-100 text-red-800',
                        label: '已婉拒',
                    };
                case 'withdrawn':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: '已撤回',
                    };
                case 'expired':
                case 'timeout':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-orange-100 text-orange-800',
                        label: '已過期',
                    };
                default:
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: status,
                    };
            }
        }

        // 系統狀態
        if (type === 'system') {
            switch (status) {
                case 'active':
                case 'open':
                case 'enabled':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-green-100 text-green-800',
                        label: '啟用',
                    };
                case 'inactive':
                case 'closed':
                case 'disabled':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-red-100 text-red-800',
                        label: '停用',
                    };
                case 'maintenance':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-orange-100 text-orange-800',
                        label: '維護中',
                    };
                case 'pending':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-yellow-100 text-yellow-800',
                        label: '待處理',
                    };
                default:
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: status,
                    };
            }
        }

        // 用戶狀態
        if (type === 'user') {
            switch (status) {
                case 'active':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-green-100 text-green-800',
                        label: '活躍',
                    };
                case 'inactive':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: '非活躍',
                    };
                case 'blocked':
                case 'banned':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-red-100 text-red-800',
                        label: '已封鎖',
                    };
                case 'pending':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-yellow-100 text-yellow-800',
                        label: '待驗證',
                    };
                default:
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: status,
                    };
            }
        }

        // Log 狀態
        if (type === 'log') {
            switch (status) {
                case 'info':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-blue-100 text-blue-800',
                        label: '資訊',
                    };
                case 'warning':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-yellow-100 text-yellow-800',
                        label: '警告',
                    };
                case 'error':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-red-100 text-red-800',
                        label: '錯誤',
                    };
                case 'debug':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: '除錯',
                    };
                case 'critical':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-purple-100 text-purple-800',
                        label: '關鍵',
                    };
                default:
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: status,
                    };
            }
        }

        // Login Log 狀態
        if (type === 'login') {
            switch (status) {
                case 'success':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-green-100 text-green-800',
                        label: '成功',
                    };
                case 'failed':
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-red-100 text-red-800',
                        label: '失敗',
                    };

                default:
                    return {
                        variant: 'secondary' as const,
                        className: 'bg-gray-100 text-gray-800',
                        label: status,
                    };
            }
        }

        // 通用狀態
        switch (status.toLowerCase()) {
            case 'success':
            case 'completed':
            case 'approved':
                return {
                    variant: 'secondary' as const,
                    className: 'bg-green-100 text-green-800',
                    label: '成功',
                };
            case 'error':
            case 'failed':
            case 'rejected':
                return {
                    variant: 'secondary' as const,
                    className: 'bg-red-100 text-red-800',
                    label: '失敗',
                };
            case 'warning':
            case 'pending':
                return {
                    variant: 'secondary' as const,
                    className: 'bg-yellow-100 text-yellow-800',
                    label: '警告',
                };
            case 'info':
            case 'processing':
                return {
                    variant: 'secondary' as const,
                    className: 'bg-blue-100 text-blue-800',
                    label: '處理中',
                };
            default:
                return {
                    variant: 'secondary' as const,
                    className: 'bg-gray-100 text-gray-800',
                    label: status,
                };
        }
    };

    const config = getStatusConfig(status, type);

    return (
        <Badge variant={config.variant} className={`${config.className} ${className}`}>
            {config.label}
        </Badge>
    );
}



