<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Models\SystemSetting;

/**
 * 歡迎頁面控制器
 */
class WelcomeController extends Controller
{
    /**
     * 顯示歡迎頁面
     */
    public function index(Request $request)
    {
        // 快取系統設定資訊1小時(電話、信箱等)
        $staticSystemInfo = Cache::remember('system_info_static', now()->addHours(1), function () {
            return [
                'support_tel' => SystemSetting::getSupportTel(),
                'support_email' => SystemSetting::getSupportEmail(),
                'admin_email' => SystemSetting::getAdminEmail(),
            ];
        });

        // 取得招生資訊
        return Inertia::render('Welcome', [
            'system_info' => $staticSystemInfo,

        ]);
    }
}
