<?php

namespace App\Services;

use App\Models\SystemSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 招生期間服務
 * 
 * 負責管理和查詢招生期間資料，替代原有的硬編碼時間限制
 */
class RecruitmentPeriodService
{
    /**
     * 快取鍵值
     */
    public  const CACHE_KEY = 'recruitment_periods_data';
    private const CACHE_TTL = 3600; // 1小時

    /**
     * 獲取所有招生期間資料
     * 
     * @return array
     */
    public function getAllPeriods(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_TTL, function () {
            $data = SystemSetting::get('recruitment_periods_data', '[]');

            if (is_string($data)) {
                $periods = json_decode($data, true);
            } else {
                $periods = $data;
            }

            return is_array($periods) ? $periods : [];
        });
    }

    /**
     * 根據exam_id獲取招生期間
     * 
     * @param string $examId
     * @return array|null
     */
    public function getPeriodByExamId(string $examId): ?array
    {
        $periods = $this->getAllPeriods();

        foreach ($periods as $period) {
            if (isset($period['exam_id']) && $period['exam_id'] === $examId) {
                return $period;
            }
        }

        return null;
    }

    /**
     * 檢查指定的exam_id是否在招生期間內
     * 
     * @param string $examId
     * @param Carbon|null $checkTime 檢查時間，預設為現在
     * @return bool
     */
    public function isInRecruitmentPeriod(string $examId, ?Carbon $checkTime = null): bool
    {
        $period = $this->getPeriodByExamId($examId);

        if (!$period) {
            Log::warning('找不到對應的招生期間', ['exam_id' => $examId]);
            return false;
        }

        $checkTime = $checkTime ?? now();

        try {
            $startTime = Carbon::parse($period['app_date1_start']);
            $endTime = Carbon::parse($period['app_date1_end']);

            $isInPeriod = $checkTime->between($startTime, $endTime);

            Log::info('招生期間檢查', [
                'exam_id' => $examId,
                'check_time' => $checkTime->toISOString(),
                'start_time' => $startTime->toISOString(),
                'end_time' => $endTime->toISOString(),
                'is_in_period' => $isInPeriod
            ]);

            return $isInPeriod;
        } catch (\Exception $e) {
            Log::error('招生期間時間解析失敗', [
                'exam_id' => $examId,
                'period' => $period,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 檢查系統是否有任何開放的招生期間
     * 
     * @param Carbon|null $checkTime
     * @return bool
     */
    public function hasActiveRecruitmentPeriod(?Carbon $checkTime = null): bool
    {
        $periods = $this->getAllPeriods();
        $checkTime = $checkTime ?? now();

        foreach ($periods as $period) {
            if (isset($period['exam_id']) && $this->isInRecruitmentPeriod($period['exam_id'], $checkTime)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 獲取當前開放的招生期間列表
     * 
     * @param Carbon|null $checkTime
     * @return array
     */
    public function getActiveRecruitmentPeriods(?Carbon $checkTime = null): array
    {
        $periods = $this->getAllPeriods();
        $checkTime = $checkTime ?? now();
        $activePeriods = [];

        foreach ($periods as $period) {
            if (isset($period['exam_id']) && $this->isInRecruitmentPeriod($period['exam_id'], $checkTime)) {
                $activePeriods[] = $period;
            }
        }

        return $activePeriods;
    }

    /**
     * 獲取招生期間的詳細資訊
     * 
     * @param string $examId
     * @return array
     */
    public function getPeriodInfo(string $examId): array
    {
        $period = $this->getPeriodByExamId($examId);

        if (!$period) {
            return [
                'exists' => false,
                'exam_id' => $examId,
                'message' => '找不到對應的招生期間'
            ];
        }

        try {
            $now = now();
            $startTime = Carbon::parse($period['app_date1_start']);
            $endTime = Carbon::parse($period['app_date1_end']);

            $status = 'unknown';
            if ($now->lt($startTime)) {
                $status = 'not_started';
            } elseif ($now->between($startTime, $endTime)) {
                $status = 'active';
            } else {
                $status = 'ended';
            }

            return [
                'exists' => true,
                'exam_id' => $period['exam_id'],
                'exam_name' => $period['exam_name'],
                'start_time' => $startTime->toISOString(),
                'end_time' => $endTime->toISOString(),
                'status' => $status,
                'is_active' => $status === 'active',
                'days_until_start' => $status === 'not_started' ? $now->diffInDays($startTime) : null,
                'days_until_end' => $status === 'active' ? $now->diffInDays($endTime) : null,
                'synced_at' => $period['synced_at'] ?? null
            ];
        } catch (\Exception $e) {
            Log::error('招生期間資訊解析失敗', [
                'exam_id' => $examId,
                'period' => $period,
                'error' => $e->getMessage()
            ]);

            return [
                'exists' => true,
                'exam_id' => $examId,
                'error' => '時間資料解析失敗',
                'raw_data' => $period
            ];
        }
    }

    /**
     * 清除快取
     */
    public function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY);
    }

    /**
     * 獲取最後同步時間
     * 
     * @return Carbon|null
     */
    public function getLastSyncTime(): ?Carbon
    {
        $syncTime = SystemSetting::get('recruitment_periods_last_sync');

        if ($syncTime) {
            try {
                return Carbon::parse($syncTime);
            } catch (\Exception $e) {
                Log::error('同步時間解析失敗', ['sync_time' => $syncTime, 'error' => $e->getMessage()]);
            }
        }

        return null;
    }

    /**
     * 檢查資料是否需要重新同步
     * 
     * @param int $maxAgeHours 最大資料年齡（小時）
     * @return bool
     */
    public function needsResync(int $maxAgeHours = 24): bool
    {
        $lastSync = $this->getLastSyncTime();

        if (!$lastSync) {
            return true; // 從未同步過
        }

        return $lastSync->addHours($maxAgeHours)->lt(now());
    }
}
