## 🧪 資料庫初始化與測試資料

### 執行 Migration 清除資料表 並填入 Seed 資料

```bash
php artisan migrate # 執行 migration 不清除資料
php artisan migrate:fresh --seed # 重置資料庫並重新放入 Seeder 資料

php artisan queue:work
php artisan queue:work --queue=pdf

php artisan queue:flush	# 清除所有失敗的任務記錄
```

---

功能：

TODO：

- 寫測試(API端v、推薦函系統端)
- 後台管理須查詢的招生代碼
    - 類別由大到小:學年度(exam_year) -> 招生類別(exam_id) -> 科系代碼(dep_no)
    - 推薦函狀態:確認中(pending)、婉拒(declined)、已完成(submitted)、已撤回(withdrawn)
- 中英文i18n
- 配置後台化(url、tel、...)
- 繳費狀態(用session保存報名狀態，後續操作以session的報名狀態做限制)
- LOG紀錄流程檢查、完成
- 參數ENV配置化

# =========================

crontab 任務排程
laravel job superviesor

1. user-agerment ch/en
