<?php

namespace App\Jobs;

use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use App\Services\PdfService;
use App\Services\FilePackagingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * PDF壓縮處理Job
 *
 * 處理PDF壓縮任務，包含檔案打包和壓縮
 */
class ProcessPdfMergeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任務超時時間（秒）
     */
    public $timeout = 1800; // 30分鐘

    /**
     * 最大重試次數
     */
    public $tries = 1;

    /**
     * 任務ID
     */
    protected string $taskId;

    /**
     * 合併參數
     */
    protected array $parameters;

    /**
     * 創建新的Job實例
     *
     * @param string $taskId 任務ID
     * @param array $parameters 壓縮參數
     */
    public function __construct(string $taskId, array $parameters)
    {
        $this->taskId = $taskId;
        $this->parameters = $parameters;
    }

    /**
     * 執行Job
     *
     * @return void
     */
    public function handle(): void
    {
        // 記錄任務開始
        Log::info('[JOB_START]', [
            'task_id' => $this->taskId,
            'test_mode' => !empty($this->parameters['test_mode'])
        ]);

        $task = PdfMergeTask::findByTaskId($this->taskId);

        if (!$task) {
            Log::error('[JOB_ERROR]', ['task_id' => $this->taskId, 'error' => 'task_not_found']);
            return;
        }

        try {
            $isTestMode = !empty($this->parameters['test_mode']);

            if ($isTestMode) {
                $recommendations = $this->generateTestRecommendations();
            } else {
                $recommendations = $this->getRecommendationsToMerge();
            }

            if ($recommendations->isEmpty()) {
                throw new \Exception('沒有找到需要壓縮的推薦函');
            }

            // 將推薦函依考生autono分組
            $groupedByRecommendationsAutono = $this->groupRecommendationsByExternalAutono($recommendations);

            if (empty($groupedByRecommendationsAutono)) {
                Log::error('[ERROR] 分組後沒有資料', [
                    'task_id' => $this->taskId,
                    'original_count' => $recommendations->count()
                ]);
                throw new \Exception('沒有找到需要壓縮的推薦函或考生資料');
            }

            Log::info('[SUCCESS] 考生分組完成', [
                'task_id' => $this->taskId,
                'applicant_count' => count($groupedByRecommendationsAutono),
                'step' => 'group_by_applicant_success'
            ]);

            // 更新任務進度
            $task->updateProgress(0, count($groupedByRecommendationsAutono));

            $filesToCompress = [];
            $processedCount = 0;

            // 遍歷每個考生的推薦函，準備檔案列表
            foreach ($groupedByRecommendationsAutono as $applicantId => $applicantRecommendations) {
                try {
                    // 獲取考生的所有推薦函PDF檔案
                    $applicantFiles = $this->prepareApplicantFiles($applicantRecommendations);

                    // 如果考生有檔案，則添加到壓縮列表
                    if (!empty($applicantFiles)) {
                        $filesToCompress = array_merge($filesToCompress, $applicantFiles);
                    }

                    $processedCount++;
                    $task->updateProgress($processedCount);
                } catch (\Exception $e) {
                    // 繼續處理其他考生
                }
            }

            if (empty($filesToCompress)) {
                throw new \Exception('沒有找到任何可壓縮的PDF檔案');
            }

            // 使用FilePackagingService進行壓縮
            $packagingService = new FilePackagingService();
            $zipFilePath = $packagingService->createDirectZipPackage(
                $filesToCompress,
                $this->taskId,
                $this->parameters
            );

            $downloadUrl = $this->generateDownloadUrl();

            $task->markAsReady($zipFilePath, $downloadUrl);

            Log::info('[JOB_COMPLETE]', [
                'task_id' => $this->taskId,
                'file_count' => count($filesToCompress),
                'zip_size_mb' => file_exists(Storage::disk('public')->path($zipFilePath))
                    ? round(filesize(Storage::disk('public')->path($zipFilePath)) / (1024 * 1024), 2)
                    : 0
            ]);
        } catch (\Exception $e) {
            Log::error('[JOB_FAILED]', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage(),
                'error_line' => $e->getLine()
            ]);

            $task->markAsFailed($e->getMessage());
        }
    }

    /**
     * 獲取需要合併的推薦函
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getRecommendationsToMerge()
    {
        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path');

        // 根據參數過濾
        if (isset($this->parameters['exam_id'])) {
            $query->where('exam_id', $this->parameters['exam_id']);
        }

        if (isset($this->parameters['exam_year'])) {
            $query->where('exam_year', $this->parameters['exam_year']);
        }

        return $query->with(['applicant', 'recommender'])->get();
    }

    /**
     * 按考生autono分組推薦函（統一物件操作）
     *
     * @param \Illuminate\Database\Eloquent\Collection $recommendations
     * @return array
     */
    protected function groupRecommendationsByExternalAutono($recommendations): array
    {
        $grouped = [];

        foreach ($recommendations as $recommendation) {
            // 統一轉換為物件操作
            if (is_array($recommendation)) {
                $recommendation = (object) $recommendation;
            }

            $externalAutono = $recommendation->external_autono ?? 'unknown';

            if (!isset($grouped[$externalAutono])) {
                $grouped[$externalAutono] = [];
            }

            $grouped[$externalAutono][] = $recommendation;
        }

        return $grouped;
    }

    /**
     * 準備考生的檔案列表（統一物件操作）
     *
     * @param array $recommendations
     * @return array
     */
    protected function prepareApplicantFiles(array $recommendations): array
    {
        $files = [];

        if (empty($recommendations)) {
            return $files;
        }

        // 統一轉換為物件操作
        $firstRec = is_array($recommendations[0]) ? (object)$recommendations[0] : $recommendations[0];

        // 獲取考生資訊
        $applicant = is_array($firstRec->applicant ?? null) ? (object)$firstRec->applicant : $firstRec->applicant;

        if (!$applicant) {
            Log::warning('[WARNING] 考生資訊不存在', [
                'recommendations' => count($recommendations)
            ]);
            return $files;
        }

        $autono = $firstRec->external_autono ?? $applicant->id ?? 'unknown';
        $fileIndex = 1;

        foreach ($recommendations as $recommendation) {
            $recommendation = is_array($recommendation) ? (object)$recommendation : $recommendation;

            $pdfPath = $recommendation->pdf_path ?? null;
            $recId = $recommendation->id ?? null;
            $applicantId = $recommendation->applicant_id ?? null;

            if (empty($pdfPath)) {
                continue;
            }

            if (!empty($this->parameters['test_mode'])) {
                // 測試模式先建立檔案
                $actualPath = $this->createTestPdfPath($pdfPath);

                if (!file_exists($actualPath)) {
                    Log::warning('[WARNING] 測試模式下檔案不存在', [
                        'recommendation_id' => $recId,
                        'pdf_path' => $pdfPath,
                        'actual_path' => $actualPath
                    ]);
                    continue;
                }
            } else {
                $disk = Storage::disk('local');

                if (!$disk->exists($pdfPath)) {
                    Log::warning('[WARNING] PDF檔案不存在', [
                        'recommendation_id' => $recId,
                        'pdf_path' => $pdfPath
                    ]);
                    continue;
                }

                $actualPath = $disk->path($pdfPath);
            }

            $files[] = [
                'source_path' => $actualPath,
                'zip_path' => "{$autono}/{$fileIndex}.pdf",
                'applicant_id' => $applicantId,
                'autono' => $autono,
                'recommendation_id' => $recId,
                'original_pdf_path' => $pdfPath,
                'file_index' => $fileIndex
            ];

            $fileIndex++;
        }

        return $files;
    }

    /**
     * 生成下載URL
     *
     * @return string
     */
    protected function generateDownloadUrl(): string
    {
        // 生成公共下載 URL（無需認證）
        return route('api.pdf-merge.public-download', [
            'taskId' => $this->taskId
        ]);
    }

    /**
     * Job失敗時的處理
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('[FINAL_FAILED] PDF壓縮Job最終失敗', [
            'task_id' => $this->taskId,
            'job_id' => $this->job->getJobId() ?? 'unknown',
            'exception_class' => get_class($exception),
            'error' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'error_file' => $exception->getFile(),
            'error_line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'attempts' => $this->attempts(),
            'max_tries' => $this->tries,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'parameters' => $this->parameters,
            'timestamp' => now()->toISOString(),
            'step' => 'job_failed_final'
        ]);

        $task = PdfMergeTask::findByTaskId($this->taskId);

        if ($task) {
            $task->markAsFailed($exception->getMessage());
            Log::info('[UPDATE] 任務狀態已更新為失敗', [
                'task_id' => $this->taskId,
                'task_status' => $task->status
            ]);
        } else {
            Log::error('[ERROR] 無法找到任務以更新失敗狀態', [
                'task_id' => $this->taskId
            ]);
        }
    }

    /**
     * 生成測試模式的PDF壓縮數據
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function generateTestRecommendations()
    {
        $fileCount = 600;
        $recommendationsPerApplicant = 3; // 每位考生有 3 封推薦函

        Log::info('[TEST_GENERATE]', [
            'task_id' => $this->taskId,
            'file_count' => $fileCount
        ]);

        $testRecommendations = collect();

        for ($i = 1; $i <= $fileCount; $i++) {
            $applicantId = ceil($i / $recommendationsPerApplicant);
            $externalAutono = $applicantId;
            $fileIndex = (($i - 1) % $recommendationsPerApplicant) + 1; // 1,2,3 循環

            $testRecommendations->push((object)[
                'id' => $i,
                'applicant_id' => $applicantId,
                'external_autono' => $externalAutono,
                'pdf_path' => "test_pdfs/2/114/{$externalAutono}/{$fileIndex}.pdf",
                'status' => 'submitted',
                'applicant' => (object)[
                    'id' => $applicantId,
                    'name' => '測試考生' . $applicantId
                ],
                'recommender' => (object)[
                    'id' => $i,
                    'name' => '測試推薦人' . $i
                ]
            ]);
        }

        return $testRecommendations;
    }

    /**
     * 創建測試PDF檔案路徑
     *
     * @param string $recommendationId
     * @return string
     */
    protected function createTestPdfPath(string $pdfPath): string
    {
        // 存在 storage/app 下的實體路徑
        $fullPath = storage_path('app/' . $pdfPath);

        // 確保目錄存在
        $dir = dirname($fullPath);
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }

        // 如果檔案不存在，就建立一個簡單的測試 PDF
        if (!file_exists($fullPath)) {
            $this->createSimpleTestPdf($fullPath);
        }

        return $fullPath;
    }


    /**
     * 創建大型測試PDF檔案（約2MB）
     *
     * @param string $filePath
     * @param string $recommendationId
     * @return void
     */
    protected function createSimpleTestPdf(string $filePath): void
    {
        // 指定範本PDF的路徑
        $templatePath = storage_path('app/test_pdfs/template.pdf');

        if (!file_exists($templatePath)) {
            // 如果範本檔案不存在，可以記錄錯誤或拋例外
            Log::error('[TEST_PDF_ERROR]', [
                'message' => 'Template PDF not found',
                'template_path' => $templatePath,
                'task_id' => $this->taskId
            ]);
            throw new \Exception('Template PDF not found for test file generation');
        }

        // 複製範本PDF作為測試PDF
        copy($templatePath, $filePath);

        Log::info('[TEST_PDF]', [
            'task_id' => $this->taskId,
            'file_path' => $filePath,
            'source_template' => $templatePath,
            'file_size_mb' => round(filesize($filePath) / (1024 * 1024), 2)
        ]);
    }
}
