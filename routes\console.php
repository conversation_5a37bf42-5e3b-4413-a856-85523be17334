<?php

use Illuminate\Support\Facades\App;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

/**
 * 排程任務
 * 
 * 將排程執行結果輸出到對應的日誌文件中
 * 這樣可以方便地查看每個任務的執行情況
 * 並且在生產環境中不會干擾到終端輸出
 * 
 * todo 定期清除日誌文件，避免佔用過多磁碟空間
 * todo 可以考慮使用 Laravel 的日誌輪替功能
 */


// ========== 排程自動狀態更新任務 - 用於更新推薦函"超時未處理"狀態 ==========
if (App::environment('production')) {
    Schedule::command('recommendations:update-status')
        ->hourly() // 每小時執行一次
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/recommendation.log'));
} else {
    Schedule::command('recommendations:update-status --dry-run')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/recommendation.log'));
}





// ========== 排程自動發送推薦函提醒郵件 - 用於發送推薦函提醒 ==========
if (App::environment('production')) {
    Schedule::command('recommendations:send-reminders')
        ->dailyAt('09:00') // 每天上午9點執行
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/email.log'));
} else {
    Schedule::command('recommendations:send-reminders --dry-run')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/email.log'));
}





// ========== 排程重試失敗郵件任務 ==========
if (App::environment('production')) {
    Schedule::command('emails:retry-failed')
        ->everyFourHours() // 每四小時執行一次
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/email.log'));
} else {
    Schedule::command('emails:retry-failed --dry-run')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/email.log'));
}





// ========== 排程清理PDF檔案任務 ==========
if (App::environment('production')) {
    Schedule::command('pdf:cleanup --days=7')
        ->dailyAt('02:00') // 每天凌晨2點執行
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/pdf-cleanup.log'));
} else {
    Schedule::command('pdf:cleanup --days=7 --dry-run')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/pdf-cleanup.log'));
}





// ========== 排程同步招生期間資料 ==========
if (App::environment('production')) {
    Schedule::command('system:sync-recruitment-periods')
        ->dailyAt('03:00') // 每天凌晨3點執行
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/recruitment-sync.log'));
} else {
    Schedule::command('system:sync-recruitment-periods')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/recruitment-sync.log'));
}
