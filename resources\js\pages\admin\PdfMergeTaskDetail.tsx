import React from 'react';
import { router, Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, RefreshCw, RotateCcw, X, Download, Trash2, FileText, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface Task {
    id: number;
    task_id: string;
    status: string;
    progress: number;
    total_files: number;
    processed_files: number;
    created_at: string;
    updated_at: string;
    expires_at?: string;
    error_message?: string;
    download_url?: string;
    zip_file_path?: string;
    parameters?: {
        exam_id?: string;
        exam_year?: number;
    };
}

interface Recommendation {
    id: number;
    status: string;
    recommender_name?: string;
    applicant?: {
        name?: string;
    };
}

interface PdfMergeTaskDetailProps {
    task: Task;
    recommendations?: Recommendation[];
    canRetry: boolean;
    canCancel: boolean;
}

export default function PdfMergeTaskDetail({ task, recommendations, canRetry, canCancel }: PdfMergeTaskDetailProps) {
    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: 'PDF合併任務管理', href: '/admin/tasks' },
        { title: '任務詳情', href: `/admin/tasks/${task.id}` },
    ];

    // 狀態相關函數
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'processing':
                return <Clock className="h-4 w-4" />;
            case 'ready':
                return <CheckCircle className="h-4 w-4" />;
            case 'failed':
                return <XCircle className="h-4 w-4" />;
            case 'expired':
                return <AlertCircle className="h-4 w-4" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    const getStatusVariant = (status: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
        switch (status) {
            case 'processing':
                return 'secondary';
            case 'ready':
                return 'default';
            case 'failed':
                return 'destructive';
            case 'expired':
                return 'outline';
            default:
                return 'outline';
        }
    };

    const getStatusText = (status: string) => {
        const statusMap = {
            processing: '處理中',
            ready: '完成',
            failed: '失敗',
            expired: '已過期',
        };
        return statusMap[status as keyof typeof statusMap] || status;
    };

    // 格式化日期
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-TW');
    };

    // 計算執行時間
    const getExecutionTime = () => {
        if (!task.created_at) return 'N/A';

        const start = new Date(task.created_at);
        const end = task.updated_at ? new Date(task.updated_at) : new Date();
        const diff = Math.floor((end.getTime() - start.getTime()) / 1000); // 秒

        if (diff < 60) return `${diff}秒`;
        if (diff < 3600) return `${Math.floor(diff / 60)}分鐘`;
        return `${Math.floor(diff / 3600)}小時`;
    };

    // 操作方法
    const goBack = () => {
        router.visit(route('admin.tasks.index'));
    };

    const refreshTask = () => {
        router.reload({ only: ['task'] });
    };

    const retryTask = () => {
        if (confirm('確定要重試此任務嗎？')) {
            router.post(route('admin.tasks.retry', task.id));
        }
    };

    const cancelTask = () => {
        if (confirm('確定要取消此任務嗎？')) {
            router.post(route('admin.tasks.cancel', task.id));
        }
    };

    const downloadTask = () => {
        if (task.status === 'ready') {
            window.open(route('admin.tasks.download', task.id), '_blank');
        }
    };

    const deleteTask = () => {
        if (confirm('確定要刪除此任務嗎？此操作無法復原。')) {
            router.delete(route('admin.tasks.destroy', task.id));
        }
    };

    const viewLogs = () => {
        router.visit(route('admin.tasks.logs', task.id));
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="任務詳情" description="查看PDF合併任務的詳細資訊和執行狀態">
            <Head title={`任務詳情 - ${task.task_id}`} />

            <div className="space-y-6 p-6">
                {/* 返回按鈕 */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={goBack}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        返回任務列表
                    </Button>
                    <div className="flex items-center gap-2">
                        {canRetry && (
                            <Button size="sm" onClick={retryTask}>
                                <RotateCcw className="mr-2 h-4 w-4" />
                                重試任務
                            </Button>
                        )}
                        {canCancel && (
                            <Button variant="destructive" size="sm" onClick={cancelTask}>
                                <X className="mr-2 h-4 w-4" />
                                取消任務
                            </Button>
                        )}
                        {task.status === 'ready' && (
                            <Button variant="outline" size="sm" onClick={downloadTask}>
                                <Download className="mr-2 h-4 w-4" />
                                下載結果
                            </Button>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* 任務基本資訊 */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    任務詳情
                                </CardTitle>
                                <CardDescription>PDF合併任務的詳細資訊和執行狀態</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* 任務狀態卡片 */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div className="rounded-lg bg-gray-50 p-4">
                                        <div className="mb-2 text-sm text-gray-600">任務狀態</div>
                                        <Badge variant={getStatusVariant(task.status)} className="flex w-fit items-center gap-2">
                                            {getStatusIcon(task.status)}
                                            {getStatusText(task.status)}
                                        </Badge>
                                    </div>

                                    <div className="rounded-lg bg-gray-50 p-4">
                                        <div className="mb-2 text-sm text-gray-600">處理進度</div>
                                        <div className="flex items-center gap-3">
                                            <div className="h-3 flex-1 rounded-full bg-gray-200">
                                                <div
                                                    className={`h-3 rounded-full transition-all duration-300 ${
                                                        task.status === 'ready'
                                                            ? 'bg-green-500'
                                                            : task.status === 'processing'
                                                              ? 'bg-blue-500'
                                                              : task.status === 'failed'
                                                                ? 'bg-red-500'
                                                                : 'bg-gray-500'
                                                    }`}
                                                    style={{ width: `${task.progress}%` }}
                                                />
                                            </div>
                                            <span className="text-sm font-medium">{task.progress}%</span>
                                        </div>
                                    </div>
                                </div>

                                {/* 任務詳細資訊 */}
                                <div className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">任務ID</label>
                                            <div className="rounded border bg-gray-100 p-2 font-mono text-sm">{task.task_id}</div>
                                        </div>

                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">考試資訊</label>
                                            <div className="rounded border bg-gray-100 p-2 text-sm">
                                                {task.parameters?.exam_id || 'N/A'} - {task.parameters?.exam_year || 'N/A'}年
                                            </div>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">總檔案數</label>
                                            <div className="text-lg font-semibold text-blue-600">{task.total_files || 0}</div>
                                        </div>

                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">已處理檔案</label>
                                            <div className="text-lg font-semibold text-green-600">{task.processed_files || 0}</div>
                                        </div>

                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">剩餘檔案</label>
                                            <div className="text-lg font-semibold text-orange-600">
                                                {(task.total_files || 0) - (task.processed_files || 0)}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">創建時間</label>
                                            <div className="text-sm text-gray-900">{formatDate(task.created_at)}</div>
                                        </div>

                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">更新時間</label>
                                            <div className="text-sm text-gray-900">{formatDate(task.updated_at)}</div>
                                        </div>
                                    </div>

                                    {task.expires_at && (
                                        <div>
                                            <label className="mb-1 block text-sm font-medium text-gray-700">過期時間</label>
                                            <div className="text-sm text-gray-900">{formatDate(task.expires_at)}</div>
                                        </div>
                                    )}

                                    {task.error_message && (
                                        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                                            <label className="mb-2 block text-sm font-medium text-red-700">錯誤訊息</label>
                                            <div className="font-mono text-sm text-red-600">{task.error_message}</div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* 側邊欄 */}
                    <div className="space-y-6">
                        {/* 快速操作 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-base">快速操作</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button variant="outline" size="sm" onClick={refreshTask} className="w-full justify-start">
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    刷新任務狀態
                                </Button>

                                {task.status === 'ready' && (
                                    <Button variant="outline" size="sm" onClick={viewLogs} className="w-full justify-start">
                                        <FileText className="mr-2 h-4 w-4" />
                                        查看處理日誌
                                    </Button>
                                )}

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={deleteTask}
                                    className="w-full justify-start text-red-600 hover:text-red-700"
                                >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    刪除任務
                                </Button>
                            </CardContent>
                        </Card>

                        {/* 相關推薦函 */}
                        {recommendations && recommendations.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-base">相關推薦函</CardTitle>
                                    <CardDescription className="text-xs">此任務涉及的推薦函列表</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    {recommendations.map((rec) => (
                                        <div key={rec.id} className="rounded-lg border border-gray-200 p-3">
                                            <div className="text-sm font-medium text-gray-900">{rec.applicant?.name || '未知考生'}</div>
                                            <div className="mt-1 text-xs text-gray-500">推薦人: {rec.recommender_name || 'N/A'}</div>
                                            <div className="text-xs text-gray-500">狀態: {rec.status}</div>
                                        </div>
                                    ))}
                                </CardContent>
                            </Card>
                        )}

                        {/* 任務統計 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-base">任務統計</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex justify-between text-sm">
                                    <span className="text-gray-600">處理效率</span>
                                    <span className="font-medium">{Math.round(((task.processed_files || 0) / (task.total_files || 1)) * 100)}%</span>
                                </div>

                                <div className="flex justify-between text-sm">
                                    <span className="text-gray-600">執行時間</span>
                                    <span className="font-medium">{getExecutionTime()}</span>
                                </div>

                                {task.zip_file_path && (
                                    <div className="flex justify-between text-sm">
                                        <span className="text-gray-600">輸出檔案</span>
                                        <span className="font-medium text-green-600">已生成</span>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
