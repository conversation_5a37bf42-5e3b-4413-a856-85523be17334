<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

/**
 * 系統日誌模型
 *
 * 記錄系統操作、登入、錯誤等各種日誌
 */
class SystemLog extends Model
{
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'operation_logs';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'type',
        'action',
        'description',
        'user_id',
        'ip_address',
        'user_agent',
        'metadata',
        'level',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * 日誌類型常數
     */
    const TYPE_LOGIN = 'login';
    const TYPE_OPERATION = 'operation';
    const TYPE_ERROR = 'error';
    const TYPE_SYSTEM = 'system';

    /**
     * 日誌級別常數
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * 操作動作常數
     */
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_VIEW = 'view';
    const ACTION_DOWNLOAD = 'download';
    const ACTION_UPLOAD = 'upload';
    const ACTION_SEND_EMAIL = 'send_email';
    const ACTION_SYSTEM_SETTING = 'system_setting';
    const ACTION_EXPORT = 'export';

    /**
     * 取得關聯的用戶
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 記錄登入日誌
     */
    public static function logLogin(User $user, bool $success = true, ?string $reason = null): void
    {
        $description = $success
            ? "用戶 {$user->name} ({$user->email}) 成功登入"
            : "用戶 {$user->email} 登入失敗" . ($reason ? ": {$reason}" : "");

        self::create([
            'type' => self::TYPE_LOGIN,
            'action' => self::ACTION_LOGIN,
            'description' => $description,
            'user_id' => $success ? $user->id : null,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'metadata' => [
                'success' => $success,
                'user_role' => $user->role,
                'reason' => $reason,
            ],
            'level' => $success ? self::LEVEL_INFO : self::LEVEL_WARNING,
        ]);
    }

    /**
     * 記錄登出日誌
     */
    public static function logLogout(User $user): void
    {
        self::create([
            'type' => self::TYPE_LOGIN,
            'action' => self::ACTION_LOGOUT,
            'description' => "用戶 {$user->name} ({$user->email}) 登出",
            'user_id' => $user->id,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'metadata' => [
                'user_role' => $user->role,
            ],
            'level' => self::LEVEL_INFO,
        ]);
    }

    /**
     * 記錄操作日誌
     */
    public static function logOperation(string $action, string $description, array $metadata = [], string $level = self::LEVEL_INFO): void
    {
        $user = Auth::user();

        self::create([
            'type' => self::TYPE_OPERATION,
            'action' => $action,
            'description' => $description,
            'user_id' => $user?->id,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'metadata' => array_merge($metadata, [
                'user_role' => $user?->role,
                'user_name' => $user?->name,
            ]),
            'level' => $level,
        ]);
    }

    /**
     * 記錄錯誤日誌
     */
    public static function logError(string $action, string $description, ?\Exception $exception = null, array $metadata = []): void
    {
        $user = Auth::user();

        $errorMetadata = $metadata;
        if ($exception) {
            $errorMetadata['exception'] = [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        self::create([
            'type' => self::TYPE_ERROR,
            'action' => $action,
            'description' => $description,
            'user_id' => $user?->id,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'metadata' => array_merge($errorMetadata, [
                'user_role' => $user?->role,
                'user_name' => $user?->name,
            ]),
            'level' => self::LEVEL_ERROR,
        ]);
    }

    /**
     * 記錄系統日誌
     */
    public static function logSystem(string $action, string $description, array $metadata = [], string $level = self::LEVEL_INFO): void
    {
        self::create([
            'type' => self::TYPE_SYSTEM,
            'action' => $action,
            'description' => $description,
            'user_id' => null,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'metadata' => $metadata,
            'level' => $level,
        ]);
    }

    /**
     * 記錄推薦函相關操作
     */
    public static function logRecommendationOperation(string $action, RecommendationLetter $recommendation, string $description, array $additionalMetadata = []): void
    {
        $metadata = array_merge([
            'recommendation_id' => $recommendation->id,
            'applicant_id' => $recommendation->applicant_id,
            'recommender_email' => $recommendation->recommender_email,
            'status' => $recommendation->status,
            'department_name' => $recommendation->department_name,
            'program_type' => $recommendation->program_type,
        ], $additionalMetadata);

        self::logOperation($action, $description, $metadata);
    }

    /**
     * 記錄郵件發送日誌
     */
    public static function logEmailSent(string $recipientEmail, string $subject, string $type, bool $success = true, ?string $errorMessage = null, array $metadata = []): void
    {
        $description = $success
            ? "成功發送 {$type} 郵件至 {$recipientEmail}"
            : "發送 {$type} 郵件至 {$recipientEmail} 失敗";

        $emailMetadata = array_merge([
            'recipient_email' => $recipientEmail,
            'subject' => $subject,
            'email_type' => $type,
            'success' => $success,
            'error_message' => $errorMessage,
        ], $metadata);

        self::logOperation(
            self::ACTION_SEND_EMAIL,
            $description,
            $emailMetadata,
            $success ? self::LEVEL_INFO : self::LEVEL_ERROR
        );
    }

    /**
     * 清理舊日誌
     */
    public static function cleanupOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);

        return self::where('created_at', '<', $cutoffDate)->delete();
    }

    /**
     * 取得日誌統計
     */
    public static function getLogStatistics(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'total_logs' => self::where('created_at', '>=', $startDate)->count(),
            'by_type' => self::where('created_at', '>=', $startDate)
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'by_level' => self::where('created_at', '>=', $startDate)
                ->selectRaw('level, COUNT(*) as count')
                ->groupBy('level')
                ->pluck('count', 'level')
                ->toArray(),
            'error_count' => self::where('created_at', '>=', $startDate)
                ->where('level', self::LEVEL_ERROR)
                ->count(),
            'login_count' => self::where('created_at', '>=', $startDate)
                ->where('type', self::TYPE_LOGIN)
                ->where('action', self::ACTION_LOGIN)
                ->count(),
        ];
    }
}
