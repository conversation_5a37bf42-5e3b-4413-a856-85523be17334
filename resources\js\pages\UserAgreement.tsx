import { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/components/ui/Button';
import { Checkbox } from '@/components/ui/Checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { User } from '@/types';
import AppLogoIcon from '@/components/AppLogoIcon';
import { AlertCircle } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';

interface Props {
    user: User;
    userRole: string;
    errors?: {
        agree?: string;
    };
}

/**
 * 考生與推薦人共同使用的個人資料使用條款頁面。
 *
 * @param user - 當前使用者的資料
 * @param userRole - 使用者角色（考生或推薦人）
 * @param errors - 可能的錯誤訊息
 * @returns
 */
export default function UserAgreement({ user, userRole, errors }: Props) {
    const [agreed, setAgreed] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [scrolledToBottom, setScrolledToBottom] = useState(false);
    const { t, language } = useLanguage();

    // 定義隱私條款內容(獨立在此文件中管理，提高維護性)
    const privacySectionsCH = [
        {
            title: '資料蒐集目的：',
            items: ['推薦函申請與管理作業', '身份驗證與系統安全維護', '相關通知與聯繫事宜'],
        },
        {
            title: '資料使用範圍：',
            items: ['僅限於推薦函系統相關業務使用', '不會將您的個人資料提供給第三方', '資料將依法妥善保存與管理'],
        },
        {
            title: '您的權利：',
            items: ['查詢、更正或刪除個人資料', '停止蒐集、處理或利用個人資料', '如有疑問可隨時聯繫系統管理員'],
        },
    ];

    const privacySectionsEN = [
        {
            title: 'Data Collection Purpose:',
            items: [
                'Recommendation letter application and management',
                'Identity verification and system security maintenance',
                'Related notifications and contact matters',
            ],
        },
        {
            title: 'Data Usage Scope:',
            items: [
                'Limited to recommendation letter system-related business use',
                'Your personal data will not be provided to third parties',
                'Data will be properly preserved and managed in accordance with the law',
            ],
        },
        {
            title: 'Your Rights:',
            items: [
                'Query, correct or delete personal data',
                'Stop collecting, processing or utilizing personal data',
                'Contact system administrator if you have any questions',
            ],
        },
    ];

    // 根據語言選擇對應的隱私條款
    const privacySections = language === 'en' ? privacySectionsEN : privacySectionsCH;

    // 提交表單處理
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!agreed) {
            return;
        }

        setIsSubmitting(true);

        router.post(
            route('user-agreement.store'),
            {
                agree: agreed,
            },
            {
                onFinish: () => setIsSubmitting(false),
            },
        );
    };

    return (
        <>
            <Head title="個人資料使用條款" />

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
                <div className="w-full max-w-2xl space-y-8">
                    <Card>
                        <CardHeader className="space-y-2 text-center">
                            <div className="flex justify-center">
                                <AppLogoIcon className="h-10 w-10" />
                            </div>
                            <CardTitle className="text-2xl">{t('使用者同意頁面.通用.標題')}</CardTitle>
                            <CardDescription className="text-sm">{t('使用者同意頁面.通用.說明')}</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2 rounded-lg bg-gray-50 p-4">
                                <div>
                                    <span className="font-medium">{t('使用者同意頁面.通用.姓名')}：</span>
                                    <span>{user.name}</span>
                                </div>
                                <div>
                                    <span className="font-medium">{t('使用者同意頁面.通用.電子信箱')}：</span>
                                    <span>{user.email}</span>
                                </div>
                                <div>
                                    <span className="font-medium">{t('使用者同意頁面.通用.聯絡電話')}：</span>
                                    <span>{user.phone || ''}</span>
                                </div>
                            </div>
                            {userRole === 'recommender' && (
                                <Alert variant="default" className="border-yellow-200 bg-yellow-50 text-yellow-800">
                                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                                    <AlertDescription className="text-sm leading-relaxed">{t('使用者同意頁面.推薦人.提示')}</AlertDescription>
                                </Alert>
                            )}

                            {/* 個人資料使用聲明 */}
                            <div
                                className="max-h-64 overflow-y-auto rounded-lg bg-blue-50 p-4 text-sm"
                                onScroll={(e) => {
                                    const el = e.currentTarget;
                                    const isBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 10;
                                    setScrolledToBottom(isBottom);
                                }}
                            >
                                {privacySections.map((section, index) => (
                                    <div key={index} className={index > 0 ? 'mt-4' : ''}>
                                        <h4 className="font-semibold text-blue-900">{section.title}</h4>
                                        <ul className="list-inside list-disc space-y-1 text-blue-800">
                                            {section.items.map((item, i) => (
                                                <li key={i}>{item}</li>
                                            ))}
                                        </ul>
                                    </div>
                                ))}
                            </div>

                            {errors?.agree && (
                                <Alert variant="destructive">
                                    <AlertDescription>{errors.agree}</AlertDescription>
                                </Alert>
                            )}

                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="agree"
                                        checked={agreed}
                                        disabled={!scrolledToBottom}
                                        onCheckedChange={(checked) => setAgreed(checked as boolean)}
                                    />
                                    <label
                                        htmlFor="agree"
                                        className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                    >
                                        {t('使用者同意頁面.通用.同意條款')}
                                    </label>
                                </div>

                                <div className="flex justify-end space-x-3">
                                    <Button type="button" variant="outline" onClick={() => router.post(route('logout'))}>
                                        {t('使用者同意頁面.通用.不同意登出')}
                                    </Button>
                                    <Button type="submit" disabled={!agreed || isSubmitting}>
                                        {isSubmitting ? t('使用者同意頁面.通用.處理中') : t('使用者同意頁面.通用.同意繼續')}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
