import AuthLayoutTemplate from '@/layouts/auth/AuthSimpleLayout';

export default function AuthLayout({
    children,
    title = '國立聯合大學',
    description,
    ...props
}: {
    children: React.ReactNode;
    title?: string;
    description: string;
}) {
    return (
        <AuthLayoutTemplate title={title} description={description} {...props}>
            {children}
        </AuthLayoutTemplate>
    );
}



