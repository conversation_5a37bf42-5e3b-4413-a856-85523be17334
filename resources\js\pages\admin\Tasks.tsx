import React, { useState, useMemo } from 'react';
import { router, Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Badge } from '@/components/ui/Badge';
import { Plus, Search, RefreshCw, Eye, RotateCcw, X, Download, FileText, Clock, CheckCircle, XCircle, AlertCircle, Filter } from 'lucide-react';

interface Task {
    id: number;
    task_id: string;
    status: string;
    progress: number;
    total_files: number;
    processed_files: number;
    created_at: string;
    updated_at: string;
    expires_at?: string;
    error_message?: string;
    download_url?: string;
    parameters?: {
        exam_id?: string;
        exam_year?: number;
    };
}

interface PdfMergeTasksProps {
    tasks: {
        data: Task[];
        current_page: number;
        last_page: number;
        from: number;
        to: number;
        total: number;
    };
    filters: {
        search?: string;
        status?: string;
    };
    statusOptions: Record<string, string>;
}

export default function TasksPage({ tasks, filters: initialFilters, statusOptions }: PdfMergeTasksProps) {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [creating, setCreating] = useState(false);
    const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
    const [filters, setFilters] = useState({
        search: initialFilters?.search || '',
        status: initialFilters?.status || '',
    });
    const [newTask, setNewTask] = useState({
        exam_id: '',
        exam_year: null as number | null,
        test_mode: false,
    });

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: 'PDF壓縮任務管理', href: '/admin/tasks' },
    ];

    // 計算分頁頁碼
    const paginationPages = useMemo(() => {
        const pages = [];
        const current = tasks.current_page;
        const last = tasks.last_page;

        const start = Math.max(1, current - 2);
        const end = Math.min(last, current + 2);

        for (let i = start; i <= end; i++) {
            pages.push(i);
        }

        return pages;
    }, [tasks.current_page, tasks.last_page]);

    // 狀態相關函數
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'processing':
                return <Clock className="h-4 w-4" />;
            case 'ready':
                return <CheckCircle className="h-4 w-4" />;
            case 'failed':
                return <XCircle className="h-4 w-4" />;
            case 'expired':
                return <AlertCircle className="h-4 w-4" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    const getStatusVariant = (status: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
        switch (status) {
            case 'processing':
                return 'secondary';
            case 'ready':
                return 'default';
            case 'failed':
                return 'destructive';
            case 'expired':
                return 'outline';
            default:
                return 'outline';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-TW');
    };

    // 搜尋防抖
    const debounceSearch = () => {
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        const timeout = setTimeout(() => {
            applyFilters();
        }, 500);
        setSearchTimeout(timeout);
    };

    // 應用過濾器
    const applyFilters = () => {
        router.get(route('admin.tasks.index'), filters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // 清除過濾器
    const clearFilters = () => {
        const newFilters = { search: '', status: '' };
        setFilters(newFilters);
        router.get(route('admin.tasks.index'), newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // 刷新任務列表
    const refreshTasks = () => {
        router.reload({ only: ['tasks'] });
    };

    // 跳轉頁面
    const goToPage = (page: number) => {
        router.get(
            route('admin.tasks.index'),
            {
                ...filters,
                page,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    // 查看任務詳情
    const viewTask = (task: Task) => {
        router.visit(route('admin.tasks.show', task.id));
    };

    // 創建任務
    const createTask = (e: React.FormEvent) => {
        e.preventDefault();
        setCreating(true);

        router.post(route('admin.tasks.store'), newTask, {
            onSuccess: () => {
                setShowCreateModal(false);
                setNewTask({ exam_id: '', exam_year: null, test_mode: false });
                setCreating(false);
            },
            onError: () => {
                setCreating(false);
            },
        });
    };

    // 重試任務
    const retryTask = (task: Task) => {
        if (confirm('確定要重試此任務嗎？')) {
            router.post(route('admin.tasks.retry', task.id));
        }
    };

    // 取消任務
    const cancelTask = (task: Task) => {
        if (confirm('確定要取消此任務嗎？')) {
            router.post(route('admin.tasks.cancel', task.id));
        }
    };

    // 下載任務結果
    const downloadTask = (task: Task) => {
        if (task.status === 'ready') {
            window.open(route('admin.tasks.download', task.id), '_blank');
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="PDF壓縮任務管理" description="管理和監控PDF壓縮任務的執行狀態">
            <Head title="PDF壓縮任務管理" />

            <div className="space-y-6 p-6">
                {/* 任務統計概覽 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-base">任務統計概覽</CardTitle>
                        <CardDescription className="text-xs">目前系統中的任務狀態分布</CardDescription>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                        {Object.entries(statusOptions).map(([status, label]) => {
                            const count = tasks.data.filter((task) => task.status === status).length;
                            return (
                                <div key={status} className="rounded-lg border bg-gray-50 p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            {getStatusIcon(status)}
                                            <span className="text-sm font-medium">{label}</span>
                                        </div>
                                        <span className="text-lg font-bold">{count}</span>
                                    </div>
                                </div>
                            );
                        })}
                    </CardContent>
                </Card>

                {/* 主要內容卡片 */}
                <Card>
                    <CardHeader className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                        <div>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                PDF壓縮任務
                            </CardTitle>
                            <CardDescription>管理和監控PDF壓縮任務的執行狀態</CardDescription>
                        </div>
                        <div className="flex gap-2">
                            <Button size="sm" onClick={() => setShowCreateModal(true)}>
                                <Plus className="mr-1 h-3 w-3" />
                                <span className="text-xs">創建新任務</span>
                            </Button>
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                    setNewTask({ exam_id: 'TEST2024', exam_year: 113, test_mode: true });
                                    setShowCreateModal(true);
                                }}
                            >
                                <FileText className="mr-1 h-3 w-3" />
                                <span className="text-xs">測試模式</span>
                            </Button>
                        </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                        {/* 搜尋和過濾 */}
                        <div className="flex flex-wrap gap-4">
                            <div className="min-w-64 flex-1">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                    <Input
                                        value={filters.search}
                                        onChange={(e) => {
                                            setFilters((prev) => ({ ...prev, search: e.target.value }));
                                            debounceSearch();
                                        }}
                                        placeholder="搜尋任務ID、考試ID或年度..."
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            <div className="min-w-32">
                                <select
                                    value={filters.status}
                                    onChange={(e) => {
                                        setFilters((prev) => ({ ...prev, status: e.target.value }));
                                        applyFilters();
                                    }}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                >
                                    <option value="">所有狀態</option>
                                    {Object.entries(statusOptions).map(([value, label]) => (
                                        <option key={value} value={value}>
                                            {label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <Button variant="outline" size="sm" onClick={clearFilters}>
                                <Filter className="mr-1 h-3 w-3" />
                                清除
                            </Button>
                            <Button variant="outline" size="sm" onClick={refreshTasks}>
                                <RefreshCw className="mr-1 h-3 w-3" />
                                刷新
                            </Button>
                        </div>

                        {/* 任務列表 */}
                        <div className="rounded-md border">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">任務ID</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                                                考試資訊
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">狀態</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">進度</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                                                檔案數量
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                                                創建時間
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white">
                                        {tasks.data.map((task) => (
                                            <tr key={task.id} className="hover:bg-gray-50">
                                                <td className="px-4 py-3 whitespace-nowrap">
                                                    <div className="font-mono text-sm text-gray-900">{task.task_id.substring(0, 20)}...</div>
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {task.parameters?.exam_id || 'N/A'} -{task.parameters?.exam_year || 'N/A'}年
                                                    </div>
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap">
                                                    <Badge variant={getStatusVariant(task.status)} className="flex w-fit items-center gap-1">
                                                        {getStatusIcon(task.status)}
                                                        {statusOptions[task.status] || task.status}
                                                    </Badge>
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap">
                                                    <div className="flex items-center gap-2">
                                                        <div className="h-2 w-16 rounded-full bg-gray-200">
                                                            <div
                                                                className={`h-2 rounded-full transition-all duration-300 ${
                                                                    task.status === 'ready'
                                                                        ? 'bg-green-500'
                                                                        : task.status === 'processing'
                                                                          ? 'bg-blue-500'
                                                                          : task.status === 'failed'
                                                                            ? 'bg-red-500'
                                                                            : 'bg-gray-500'
                                                                }`}
                                                                style={{ width: `${task.progress}%` }}
                                                            />
                                                        </div>
                                                        <span className="text-xs text-gray-600">{task.progress}%</span>
                                                    </div>
                                                </td>
                                                <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-900">
                                                    {task.processed_files || 0} / {task.total_files || 0}
                                                </td>
                                                <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-500">{formatDate(task.created_at)}</td>
                                                <td className="px-4 py-3 text-sm font-medium whitespace-nowrap">
                                                    <div className="flex items-center gap-2">
                                                        <Button variant="ghost" size="sm" onClick={() => viewTask(task)}>
                                                            <Eye className="h-3 w-3" />
                                                        </Button>
                                                        {(task.status === 'failed' || task.status === 'expired') && (
                                                            <Button variant="ghost" size="sm" onClick={() => retryTask(task)}>
                                                                <RotateCcw className="h-3 w-3" />
                                                            </Button>
                                                        )}
                                                        {task.status === 'processing' && (
                                                            <Button variant="ghost" size="sm" onClick={() => cancelTask(task)}>
                                                                <X className="h-3 w-3" />
                                                            </Button>
                                                        )}
                                                        {task.status === 'ready' && (
                                                            <Button variant="ghost" size="sm" onClick={() => downloadTask(task)}>
                                                                <Download className="h-3 w-3" />
                                                            </Button>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        {/* 分頁 */}
                        {tasks.last_page > 1 && (
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    顯示 {tasks.from} 到 {tasks.to} 筆，共 {tasks.total} 筆
                                </div>
                                <div className="flex space-x-1">
                                    {paginationPages.map((page) => (
                                        <Button
                                            key={page}
                                            variant={page === tasks.current_page ? 'default' : 'outline'}
                                            size="sm"
                                            onClick={() => goToPage(page)}
                                        >
                                            {page}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* 創建任務模態框 */}
            {showCreateModal && (
                <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                    <Card className="w-full max-w-md">
                        <CardHeader>
                            <CardTitle>創建PDF壓縮任務</CardTitle>
                            <CardDescription>
                                {newTask.test_mode ? '測試模式：將產生約3GB的測試檔案進行壓縮' : '指定考試ID和年度來創建新的壓縮任務'}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={createTask} className="space-y-4">
                                <div>
                                    <Label htmlFor="exam_id">考試ID</Label>
                                    <Input
                                        id="exam_id"
                                        value={newTask.exam_id}
                                        onChange={(e) => setNewTask((prev) => ({ ...prev, exam_id: e.target.value }))}
                                        placeholder="例如：2、3..."
                                        required
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="exam_year">考試年度（民國年）</Label>
                                    <Input
                                        id="exam_year"
                                        type="number"
                                        value={newTask.exam_year || ''}
                                        onChange={(e) => setNewTask((prev) => ({ ...prev, exam_year: parseInt(e.target.value) || null }))}
                                        placeholder="例如：113"
                                        min="100"
                                        max="200"
                                        required
                                    />
                                </div>

                                <div className="flex items-center space-x-2">
                                    <input
                                        id="test_mode"
                                        type="checkbox"
                                        checked={newTask.test_mode}
                                        onChange={(e) => setNewTask((prev) => ({ ...prev, test_mode: e.target.checked }))}
                                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                    <Label htmlFor="test_mode" className="text-sm">
                                        測試模式（產生約3GB測試檔案）
                                    </Label>
                                </div>

                                <div className="flex justify-end space-x-2">
                                    <Button type="button" variant="outline" onClick={() => setShowCreateModal(false)}>
                                        取消
                                    </Button>
                                    <Button type="submit" disabled={creating}>
                                        {creating ? '創建中...' : '創建任務'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            )}
        </AdminLayout>
    );
}



