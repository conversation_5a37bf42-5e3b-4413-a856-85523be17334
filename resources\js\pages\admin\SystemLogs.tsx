import AdminLayout from '@/layouts/AdminLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Input } from '@/components/ui/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { Eye, Activity, AlertTriangle, Info, XCircle, User, Shield } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import DataTable from '@/components/admin/DataTable';

interface SystemLog {
    id: number;
    user_id?: number;
    type: string;
    action: string;
    description: string;
    level: string;
    ip_address: string;
    user_agent: string;
    metadata?: any;
    context?: any;
    created_at: string;
    updated_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface SystemLogsProps {
    logs: {
        data: SystemLog[];
        current_page: number;
        last_page: number;
        from: number;
        to: number;
        total: number;
    };
}

export default function SystemLogs({ logs }: SystemLogsProps) {
    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統日誌', href: '/admin/system-logs' },
    ];

    const [searchTerm, setSearchTerm] = useState('');
    const [typeFilter, setTypeFilter] = useState('all');
    const [levelFilter, setLevelFilter] = useState('all');

    // 格式化日期
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-TW');
    };

    // 獲取日誌等級徽章
    const getLevelBadge = (level: string) => {
        switch (level) {
            case 'error':
                return (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                        <XCircle className="mr-1 h-3 w-3" />
                        錯誤
                    </Badge>
                );
            case 'warning':
                return (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="mr-1 h-3 w-3" />
                        警告
                    </Badge>
                );
            case 'info':
                return (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        <Info className="mr-1 h-3 w-3" />
                        資訊
                    </Badge>
                );
            case 'debug':
                return (
                    <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                        <Activity className="mr-1 h-3 w-3" />
                        除錯
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{level}</Badge>;
        }
    };

    // 獲取日誌類型徽章
    const getTypeBadge = (type: string) => {
        switch (type) {
            case 'operation':
                return (
                    <Badge variant="outline" className="text-blue-700">
                        操作
                    </Badge>
                );
            case 'security':
                return (
                    <Badge variant="outline" className="text-red-700">
                        安全
                    </Badge>
                );
            case 'system':
                return (
                    <Badge variant="outline" className="text-green-700">
                        系統
                    </Badge>
                );
            case 'error':
                return (
                    <Badge variant="outline" className="text-orange-700">
                        錯誤
                    </Badge>
                );
            case 'audit':
                return (
                    <Badge variant="outline" className="text-purple-700">
                        稽核
                    </Badge>
                );
            default:
                return <Badge variant="outline">{type}</Badge>;
        }
    };

    // 查看詳情
    const handleViewDetail = (log: SystemLog) => {
        router.visit(route('admin.system-logs.show', log.id));
    };

    // 定義表格欄位
    const columns = [
        {
            key: 'created_at',
            label: '時間',
            sortable: true,
            render: (value: string) => <div className="text-sm">{formatDate(value)}</div>,
        },
        {
            key: 'level',
            label: '等級',
            render: (value: string) => getLevelBadge(value),
        },
        {
            key: 'type',
            label: '類型',
            render: (value: string) => getTypeBadge(value),
        },
        {
            key: 'action',
            label: '動作',
            render: (value: string) => <div className="text-sm font-medium">{value}</div>,
        },
        {
            key: 'description',
            label: '描述',
            render: (value: string) => (
                <div className="max-w-xs truncate" title={value}>
                    {value}
                </div>
            ),
        },
        {
            key: 'user',
            label: '使用者',
            render: (value: any, row: SystemLog) => {
                if (!value) return <span className="text-gray-400">系統</span>;
                return (
                    <div>
                        <div className="text-sm font-medium">{value.name}</div>
                        <div className="text-xs text-gray-500">{value.email}</div>
                        <div className="mt-1">
                            {value.role === 'admin' ? (
                                <Badge variant="secondary" className="bg-red-100 text-red-800">
                                    <Shield className="mr-1 h-3 w-3" />
                                    管理員
                                </Badge>
                            ) : (
                                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                    <User className="mr-1 h-3 w-3" />
                                    用戶
                                </Badge>
                            )}
                        </div>
                    </div>
                );
            },
        },
        {
            key: 'ip_address',
            label: 'IP 位址',
            render: (value: string) => <div className="font-mono text-sm">{value}</div>,
        },
    ];

    // 定義操作按鈕
    const actions = [
        {
            key: 'view',
            label: '查看',
            icon: <Eye className="h-3 w-3" />,
            onClick: handleViewDetail,
            variant: 'outline' as const,
        },
    ];

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="系統日誌" description="查看和管理系統操作記錄和錯誤日誌">
            <Head title="系統日誌" />

            <div className="space-y-6 p-6">
                {/* 搜尋和過濾 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Activity className="h-5 w-5" />
                            搜尋和過濾
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-4">
                            <div className="min-w-64 flex-1">
                                <Input placeholder="搜尋動作、描述或使用者..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                            </div>
                            <div className="min-w-32">
                                <Select value={levelFilter} onValueChange={setLevelFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="等級" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">所有等級</SelectItem>
                                        <SelectItem value="error">錯誤</SelectItem>
                                        <SelectItem value="warning">警告</SelectItem>
                                        <SelectItem value="info">資訊</SelectItem>
                                        <SelectItem value="debug">除錯</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="min-w-32">
                                <Select value={typeFilter} onValueChange={setTypeFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="類型" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">所有類型</SelectItem>
                                        <SelectItem value="operation">操作</SelectItem>
                                        <SelectItem value="security">安全</SelectItem>
                                        <SelectItem value="system">系統</SelectItem>
                                        <SelectItem value="error">錯誤</SelectItem>
                                        <SelectItem value="audit">稽核</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Activity className="h-8 w-8 text-blue-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">總計</p>
                                    <p className="text-2xl font-bold text-gray-900">{logs.total}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <XCircle className="h-8 w-8 text-red-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">錯誤</p>
                                    <p className="text-2xl font-bold text-gray-900">{logs.data.filter((log) => log.level === 'error').length}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <AlertTriangle className="h-8 w-8 text-yellow-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">警告</p>
                                    <p className="text-2xl font-bold text-gray-900">{logs.data.filter((log) => log.level === 'warning').length}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Info className="h-8 w-8 text-green-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">資訊</p>
                                    <p className="text-2xl font-bold text-gray-900">{logs.data.filter((log) => log.level === 'info').length}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* 資料表格 */}
                <DataTable
                    data={logs.data.filter((log) => {
                        const matchesSearch =
                            log.action?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            log.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            log.user?.name?.toLowerCase().includes(searchTerm.toLowerCase());

                        const matchesLevel = levelFilter === 'all' || log.level === levelFilter;
                        const matchesType = typeFilter === 'all' || log.type === typeFilter;

                        return matchesSearch && matchesLevel && matchesType;
                    })}
                    columns={columns}
                    actions={actions}
                    title="系統日誌列表"
                    description={`顯示第 ${logs.from} 到 ${logs.to} 筆，共 ${logs.total} 筆記錄`}
                    emptyMessage="沒有找到符合條件的系統日誌"
                    exportable={true}
                />
            </div>
        </AdminLayout>
    );
}
