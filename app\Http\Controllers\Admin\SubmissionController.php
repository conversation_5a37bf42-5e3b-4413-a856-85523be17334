<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use App\Models\SystemSetting;
use App\Models\SystemLog;
use App\Services\DataMergeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

/**
 * 提交管理控制器
 * 
 * 統一管理推薦函的提交方式，包括PDF上傳和問卷填寫功能
 * 整合原本的PdfManagementController和QuestionnaireController功能
 */
class SubmissionController extends Controller
{
    /**
     * 顯示提交管理頁面
     * 整合問卷模板管理和提交方式設定
     */
    public function index()
    {
        // 獲取問卷模板列表
        $templates = QuestionnaireTemplate::orderBy('department_name')
            ->orderBy('program_type')
            ->get()
            ->map(function ($template) {
                return [
                    'id' => $template->id,
                    'template_name' => $template->template_name,
                    'department_name' => $template->department_name,
                    'program_type' => $template->program_type,
                    'is_active' => $template->is_active,
                    'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                    'creator_name' => '系統',
                    'questions_count' => count($template->questions ?? [])
                ];
            });

        $breadcrumbs = [
            ['title' => '儀表板', 'href' => '/dashboard'],
            ['title' => '提交管理', 'href' => '/admin/submission'],
        ];

        return Inertia::render('admin/SubmissionManagement', [
            'templates' => $templates,
            'breadcrumbs' => $breadcrumbs,
            'submission_settings' => [
                'allow_pdf_upload' => SystemSetting::isAllowPdfUpload(),
                'allow_questionnaire_submission' => SystemSetting::isAllowQuestionnaireSubmission(),
                'max_upload_size' => config('recommendation.upload.max_size'),
                'allowed_types' => config('recommendation.upload.allowed_types'),
                'storage_path' => config('recommendation.pdf.storage.path'),
            ]
        ]);
    }

    /**
     * 更新提交方式設定
     */
    public function updateSubmissionSettings(Request $request)
    {
        $request->validate([
            'allow_pdf_upload' => 'required|boolean',
            'allow_questionnaire_submission' => 'required|boolean',
        ]);

        try {
            // 至少要允許一種提交方式
            if (!$request->allow_pdf_upload && !$request->allow_questionnaire_submission) {
                return back()->withErrors([
                    'submission_settings' => '至少要允許一種提交方式'
                ]);
            }

            SystemSetting::setAllowPdfUpload($request->allow_pdf_upload);
            SystemSetting::setAllowQuestionnaireSubmission($request->allow_questionnaire_submission);

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_UPDATE,
                "管理員 {$user->name} 更新了提交方式設定",
                [
                    'allow_pdf_upload' => $request->allow_pdf_upload,
                    'allow_questionnaire_submission' => $request->allow_questionnaire_submission,
                ]
            );

            return back()->with('success', '提交方式設定已更新');
        } catch (\Exception $e) {
            Log::error('提交方式設定更新失敗', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return back()->withErrors([
                'submission_settings' => '設定更新失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 啟用/停用問卷模板
     */
    public function toggleTemplate($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);
            $template->update(['is_active' => !$template->is_active]);

            $user = Auth::user();
            $action = $template->is_active ? '啟用' : '停用';

            SystemLog::logOperation(
                SystemLog::ACTION_UPDATE,
                "管理員 {$user->name} {$action}了問卷模板: {$template->template_name}",
                [
                    'template_id' => $id,
                    'template_name' => $template->template_name,
                    'is_active' => $template->is_active,
                ]
            );

            return back()->with('success', "問卷模板已{$action}");
        } catch (\Exception $e) {
            Log::error('問卷模板狀態切換失敗', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'template' => '模板狀態更新失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 上傳 CSV 檔案並創建問卷模板
     */
    public function uploadCsvTemplate(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt',
            'department_name' => 'required|string',
            'program_type' => 'required|string',
        ]);

        try {
            $file = $request->file('csv_file');
            $csvData = $this->parseCsvFile($file);

            if (empty($csvData)) {
                return back()->withErrors(['csv_file' => 'CSV 檔案格式錯誤或為空']);
            }

            $template = QuestionnaireTemplate::createFromCsv(
                $request->department_name,
                $request->program_type,
                $csvData
            );

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_CREATE,
                "管理員 {$user->name} 上傳了問卷模板CSV: {$template->template_name}",
                [
                    'template_id' => $template->id,
                    'department_name' => $request->department_name,
                    'program_type' => $request->program_type,
                ]
            );

            return back()->with([
                'success' => '問卷模板已成功創建',
                'template' => $template,
            ]);
        } catch (\Exception $e) {
            Log::error('CSV問卷模板上傳失敗', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->except('csv_file')
            ]);

            return back()->withErrors([
                'csv_file' => '問卷模板創建失敗: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 儲存問卷模板
     */
    public function saveTemplate(Request $request)
    {
        $request->validate([
            'department_name' => 'required|string',
            'program_type' => 'required|string',
            'template_name' => 'required|string',
            'questions' => 'required|array',
            'questions.*.question' => 'required|string',
            'questions.*.type' => 'required|string|in:text,textarea,select,radio,checkbox',
        ]);

        try {
            // 停用該系所/學程的現有模板
            QuestionnaireTemplate::where('department_name', $request->department_name)
                ->where('program_type', $request->program_type)
                ->update(['is_active' => false]);

            // 創建新模板
            $template = QuestionnaireTemplate::create([
                'department_name' => $request->department_name,
                'program_type' => $request->program_type,
                'template_name' => $request->template_name,
                'questions' => $request->questions,
                'is_active' => true,
            ]);

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_CREATE,
                "管理員 {$user->name} 創建了問卷模板: {$template->template_name}",
                [
                    'template_id' => $template->id,
                    'department_name' => $request->department_name,
                    'program_type' => $request->program_type,
                ]
            );

            return back()->with('success', '問卷模板已保存');
        } catch (\Exception $e) {
            Log::error('問卷模板保存失敗', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->except('questions')
            ]);

            return back()->withErrors([
                'template' => '問卷模板保存失敗: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 刪除問卷模板
     */
    public function destroyTemplate($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);
            $templateName = $template->template_name;

            // 標記為非活動而非實際刪除
            $template->update(['is_active' => false]);

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_DELETE,
                "管理員 {$user->name} 刪除了問卷模板: {$templateName}",
                [
                    'template_id' => $id,
                    'template_name' => $templateName,
                ]
            );

            return back()->with('success', '問卷模板已刪除');
        } catch (\Exception $e) {
            Log::error('問卷模板刪除失敗', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'delete' => '刪除問卷模板失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 解析CSV檔案
     */
    private function parseCsvFile($file): array
    {
        $csvData = [];
        $handle = fopen($file->getRealPath(), 'r');

        if ($handle === false) {
            return [];
        }

        // 讀取標題行
        $headers = fgetcsv($handle);
        if (!$headers) {
            fclose($handle);
            return [];
        }

        // 讀取資料行
        while (($row = fgetcsv($handle)) !== false) {
            if (count($row) === count($headers)) {
                $csvData[] = array_combine($headers, $row);
            }
        }

        fclose($handle);
        return $csvData;
    }

    /**
     * 取得推薦函的問卷模板
     */
    public function getTemplate($recommendationId)
    {
        $user = Auth::user();

        $recommendation = RecommendationLetter::where('id', $recommendationId)
            ->where('recommender_email', $user->email)
            ->first();

        if (!$recommendation) {
            abort(404, '推薦函不存在或您無權限查看');
        }

        // 獲取推薦函相關的問卷模板
        $template = QuestionnaireTemplate::getTemplate(
            $recommendation->department_name,
            $recommendation->program_type
        );

        // 如果沒有找到模板，則使用預設模板
        if (!$template) {
            $template = QuestionnaireTemplate::getDefaultTemplate();
        }

        // 如果模板不存在，則返回空模板
        if (!$template) {
            return response()->json([
                'template' => null,
                'recommendation' => $recommendation,
            ]);
        }

        // 確保問題是陣列格式
        if ($template && is_string($template->questions)) {
            $template->questions = json_decode($template->questions, true);
        }

        return response()->json([
            'template' => $template, // 返回問卷模板
            'recommendation' => $recommendation, // 返回推薦函資料
        ]);
    }

    /**
     * 獲取設定資訊
     */
    public function getSettings()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'submission_settings' => [
                    'allow_pdf_upload' => SystemSetting::isAllowPdfUpload(),
                    'allow_questionnaire_submission' => SystemSetting::isAllowQuestionnaireSubmission(),
                ],
                'upload' => config('recommendation.upload'),
                'pdf' => config('recommendation.pdf'),
                'storage_info' => [
                    'disk' => config('recommendation.pdf.storage.disk'),
                    'path' => config('recommendation.pdf.storage.path'),
                    'available_space' => $this->getAvailableStorageSpace()
                ]
            ]
        ]);
    }

    /**
     * 獲取可用存儲空間
     */
    private function getAvailableStorageSpace(): string
    {
        try {
            $disk = Storage::disk('local');
            $path = $disk->path('');
            $bytes = disk_free_space($path);

            if ($bytes === false) {
                return '無法獲取';
            }

            $units = ['B', 'KB', 'MB', 'GB', 'TB'];
            $bytes = max($bytes, 0);
            $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
            $pow = min($pow, count($units) - 1);

            $bytes /= (1 << (10 * $pow));

            return round($bytes, 2) . ' ' . $units[$pow];
        } catch (\Exception $e) {
            Log::warning('無法獲取存儲空間資訊', ['error' => $e->getMessage()]);
            return '無法獲取';
        }
    }
}
