import { Head, <PERSON> } from '@inertiajs/react';
import { Calendar, Lock, Shield, Home, Clock } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';

interface SystemAccessDeniedProps {
    title: string;
    message: string;
    icon: string;
    recruitment_period: {
        start: string | null;
        end: string | null;
        is_active: boolean;
    };
}

export default function SystemAccessDenied({ 
    title, 
    message, 
    icon, 
    recruitment_period 
}: SystemAccessDeniedProps) {
    const getIcon = () => {
        switch (icon) {
            case 'lock':
                return <Lock className="h-16 w-16 text-red-500" />;
            case 'calendar':
                return <Calendar className="h-16 w-16 text-orange-500" />;
            case 'shield':
                return <Shield className="h-16 w-16 text-gray-500" />;
            default:
                return <Lock className="h-16 w-16 text-red-500" />;
        }
    };

    const formatDateTime = (dateTime: string | null) => {
        if (!dateTime) return '未設定';
        return new Date(dateTime).toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <>
            <Head title={title} />
            
            <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
                <div className="sm:mx-auto sm:w-full sm:max-w-md">
                    <div className="flex justify-center">
                        {getIcon()}
                    </div>
                    <h1 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
                        {title}
                    </h1>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        {message}
                    </p>
                </div>

                <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-base">
                                <Clock className="h-4 w-4" />
                                招生時程資訊
                            </CardTitle>
                            <CardDescription className="text-xs">
                                系統開放時間受招生時程控制
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-700">招生狀態</span>
                                <Badge 
                                    variant={recruitment_period.is_active ? "default" : "secondary"}
                                    className={recruitment_period.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                                >
                                    {recruitment_period.is_active ? '進行中' : '未開始/已結束'}
                                </Badge>
                            </div>
                            
                            <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                    <span className="text-xs text-gray-600">開始時間</span>
                                    <span className="text-xs font-medium">
                                        {formatDateTime(recruitment_period.start)}
                                    </span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-xs text-gray-600">結束時間</span>
                                    <span className="text-xs font-medium">
                                        {formatDateTime(recruitment_period.end)}
                                    </span>
                                </div>
                            </div>

                            {!recruitment_period.is_active && recruitment_period.start && (
                                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                    <p className="text-xs text-blue-800">
                                        <strong>提醒：</strong>
                                        {new Date(recruitment_period.start) > new Date() 
                                            ? `招生將於 ${formatDateTime(recruitment_period.start)} 開始`
                                            : '招生期間已結束，請關注下次招生公告'
                                        }
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <div className="mt-6 flex flex-col space-y-3">
                        <Button asChild className="w-full">
                            <Link href="/">
                                <Home className="mr-2 h-4 w-4" />
                                返回首頁
                            </Link>
                        </Button>
                        
                        <Button variant="outline" asChild className="w-full">
                            <Link href="/login">
                                重新登入
                            </Link>
                        </Button>
                    </div>

                    <div className="mt-6 text-center">
                        <p className="text-xs text-gray-500">
                            如有疑問，請聯繫系統管理員
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}



