import { useLanguage } from '@/hooks/useLanguage';
import AppLogoIcon from './AppLogoIcon';

export default function AppLogo() {
    const { t } = useLanguage();
    return (
        <>
            <div className="flex aspect-square size-8 items-center justify-center rounded-md text-sidebar-primary-foreground">
                <AppLogoIcon />
            </div>
            <div className="ml-1 grid flex-1 text-left text-sm">
                {/* 大裝置 */}
                <span className="mb-0.5 hidden truncate leading-tight font-semibold sm:inline">
                    {t('通用.學校名稱')} – {t('通用.系統名稱')}
                </span>

                {/* 小裝置 */}
                <span className="mb-0.5 truncate leading-tight font-semibold sm:hidden">
                    {t('通用.學校名稱縮寫')} – {t('通用.系統名稱')}
                </span>
            </div>
        </>
    );
}
