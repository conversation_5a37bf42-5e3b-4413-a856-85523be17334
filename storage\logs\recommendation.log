開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
開始檢查推薦函狀態...
使用超時天數: 7 天
*** 這是預覽模式，不會實際更新資料 ***
沒有找到需要標記為超時的推薦函
沒有找到需要發送提醒的推薦函

=== 執行結果 ===
預覽模式結果:
  - 將標記為超時: 0 筆
  - 將發送提醒: 0 筆
推薦函狀態檢查完成
